"use client";
import React, { useState } from "react";
import <PERSON>ram<PERSON> from "react-iframe";
import useTVShowStore from "@/store/tvShowStore";
import { Play, Maximize, Volume2, Settings, Server, ChevronDown, List } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { getImageUrl } from "@/lib/image";
import { VIDEO_SOURCES } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

function TVShowPlayer() {
  const { tvShowDetailsData } = useTVShowStore();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentServer, setCurrentServer] = useState("server 1");
  const [selectedSeason, setSelectedSeason] = useState("1");
  const [selectedEpisode, setSelectedEpisode] = useState("1");

  if (!tvShowDetailsData) {
    return (
      <div className='w-full aspect-video bg-zinc-900 flex items-center justify-center'>
        <div className="w-12 h-12 border-4 border-primary-red border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const handlePlay = () => {
    setIsPlaying(true);
  };
  
  const handleServerChange = (server) => {
    setCurrentServer(server);
    // If already playing, no need to set isPlaying again
    if (!isPlaying) {
      setIsPlaying(true);
    }
  };

  const handleSeasonChange = (value) => {
    setSelectedSeason(value);
    setSelectedEpisode("1"); // Reset to episode 1 when season changes
  };

  const handleEpisodeChange = (value) => {
    setSelectedEpisode(value);
  };
  
  // Get the video source URL based on the selected server
  const getVideoSourceUrl = () => {
    const serverFunction = VIDEO_SOURCES.tv[currentServer];
    if (serverFunction) {
      return serverFunction(tvShowDetailsData.id, selectedSeason, selectedEpisode);
    }
    // Fallback to server 1 if the selected server doesn't exist
    return VIDEO_SOURCES.tv["server 1"](tvShowDetailsData.id, selectedSeason, selectedEpisode);
  };

  // Get available seasons
  const getSeasons = () => {
    if (!tvShowDetailsData.seasons) return [];
    return tvShowDetailsData.seasons.filter(season => season.season_number > 0);
  };

  // Get available episodes for the selected season
  const getEpisodes = () => {
    const seasonDetails = tvShowDetailsData.seasonsDetails?.find(
      season => season.season_number === parseInt(selectedSeason)
    );
    
    if (!seasonDetails || !seasonDetails.episodes) {
      // If we don't have episode details, create a default list based on episode count
      const selectedSeasonData = tvShowDetailsData.seasons?.find(
        season => season.season_number === parseInt(selectedSeason)
      );
      
      if (selectedSeasonData && selectedSeasonData.episode_count) {
        return Array.from({ length: selectedSeasonData.episode_count }, (_, i) => ({
          episode_number: i + 1,
          name: `Episode ${i + 1}`
        }));
      }
      
      return [];
    }
    
    return seasonDetails.episodes;
  };
  
  return (
    <div className='w-full aspect-video bg-zinc-900 overflow-hidden group relative'>
      {!isPlaying ? (
        <>
          {/* Backdrop Image */}
          {tvShowDetailsData.backdrop_path && (
            <div className='absolute inset-0'>
              <img 
                src={getImageUrl(tvShowDetailsData.backdrop_path, "w780")}
                alt={tvShowDetailsData.name}
                className='w-full h-full object-cover'
              />
              {/* Gradient Overlay */}
              <div className='absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/40' />
            </div>
          )}
          
          {/* Play Button */}
          <div className="absolute inset-0 flex flex-col items-center justify-center z-20">
            <Button 
              onClick={handlePlay}
              className="bg-primary-red hover:bg-primary-red/90 text-white rounded-full w-16 h-16 flex items-center justify-center mb-4"
            >
              <Play className="h-8 w-8 ml-1" />
            </Button>
            <h2 className="text-2xl font-bold text-white mb-2">{tvShowDetailsData.name}</h2>
            <p className="text-gray-300 text-sm max-w-md text-center mb-4">
              Season {selectedSeason}, Episode {selectedEpisode}
            </p>
            
            <div className="flex flex-wrap gap-3 justify-center mb-4">
              {/* Season Selection */}
              <Select value={selectedSeason} onValueChange={handleSeasonChange}>
                <SelectTrigger className="w-[140px] bg-black/30 backdrop-blur-sm border-gray-700/50">
                  <SelectValue placeholder="Season" />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                  {getSeasons().map((season) => (
                    <SelectItem key={season.id} value={season.season_number.toString()}>
                      Season {season.season_number}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Episode Selection */}
              <Select value={selectedEpisode} onValueChange={handleEpisodeChange}>
                <SelectTrigger className="w-[140px] bg-black/30 backdrop-blur-sm border-gray-700/50">
                  <SelectValue placeholder="Episode" />
                </SelectTrigger>
                <SelectContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50 max-h-[300px]">
                  {getEpisodes().map((episode) => (
                    <SelectItem key={episode.id || episode.episode_number} value={episode.episode_number.toString()}>
                      Ep {episode.episode_number}: {episode.name.length > 15 ? episode.name.substring(0, 15) + '...' : episode.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Server Selection */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="bg-black/30 backdrop-blur-sm border-gray-700/50 hover:bg-black/50">
                    <Server className="h-4 w-4 mr-2" />
                    {currentServer.charAt(0).toUpperCase() + currentServer.slice(1)}
                    <ChevronDown className="h-4 w-4 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                  {Object.keys(VIDEO_SOURCES.tv || {}).map((server) => (
                    <DropdownMenuItem 
                      key={server}
                      className={`cursor-pointer ${currentServer === server ? 'bg-primary-red/20 text-primary-red' : ''}`}
                      onClick={() => handleServerChange(server)}
                    >
                      {server.charAt(0).toUpperCase() + server.slice(1)}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
          
        </>
      ) : (
        <>
          {/* Gradient Overlay */}
          <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40 z-10' />
          
          {/* Controls when playing */}
          <div className="absolute top-4 right-4 z-30 flex gap-2">
            {/* Season/Episode Selection */}
            <Select value={selectedSeason} onValueChange={handleSeasonChange}>
              <SelectTrigger className="w-[100px] bg-black/50 backdrop-blur-sm border-gray-700/50 h-9">
                <SelectValue placeholder="Season" />
              </SelectTrigger>
              <SelectContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                {getSeasons().map((season) => (
                  <SelectItem key={season.id} value={season.season_number.toString()}>
                    Season {season.season_number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedEpisode} onValueChange={handleEpisodeChange}>
              <SelectTrigger className="w-[100px] bg-black/50 backdrop-blur-sm border-gray-700/50 h-9">
                <SelectValue placeholder="Episode" />
              </SelectTrigger>
              <SelectContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50 max-h-[300px]">
                {getEpisodes().map((episode) => (
                  <SelectItem key={episode.id || episode.episode_number} value={episode.episode_number.toString()}>
                    Ep {episode.episode_number}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Server Selection */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="bg-black/50 backdrop-blur-sm border-gray-700/50 hover:bg-black/70 h-9">
                  <Server className="h-4 w-4 mr-2" />
                  {currentServer.charAt(0).toUpperCase() + currentServer.slice(1)}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                {Object.keys(VIDEO_SOURCES.tv || {}).map((server) => (
                  <DropdownMenuItem 
                    key={server}
                    className={`cursor-pointer ${currentServer === server ? 'bg-primary-red/20 text-primary-red' : ''}`}
                    onClick={() => handleServerChange(server)}
                  >
                    {server.charAt(0).toUpperCase() + server.slice(1)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* TV Show Player */}
          <Iframe
            url={getVideoSourceUrl()}
            width='100%'
            height='100%'
            display='block'
            position='relative'
            className='z-20'
            id="tvshow-iframe"
            allowFullScreen
          />
        </>
      )}
    </div>
  );
}

export default TVShowPlayer;
