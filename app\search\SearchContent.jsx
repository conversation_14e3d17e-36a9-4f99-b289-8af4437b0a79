"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { Film, Tv, Layers, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import DiscoveryGrid from "@/components/DiscoveryGrid";
import useSearchStore from "@/store/searchStore";
import SkeletonSearchResults from "@/components/skeletons/SkeletonSearchResults";

export default function SearchContent() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";
  const [searchInput, setSearchInput] = useState(query);

  const {
    searchResults,
    searchLoading,
    searchError,
    searchHasMore,
    searchQuery,
    searchType,
    setSearchQuery,
    setSearchType,
    loadMoreResults,
    resetSearch,
  } = useSearchStore();

  // Initialize search when page loads with query parameter
  useEffect(() => {
    if (query) {
      setSearchInput(query);
      setSearchQuery(query);
    } else {
      resetSearch();
    }
  }, [query, setSearchQuery, resetSearch]);

  // Handle search form submission
  const handleSearch = (e) => {
    e.preventDefault();
    if (searchInput.trim()) {
      setSearchQuery(searchInput.trim());

      // Update URL with search query without page reload
      const url = new URL(window.location);
      url.searchParams.set("q", searchInput.trim());
      window.history.pushState({}, "", url);
    }
  };

  // Filter buttons for search type
  const filterButtons = [
    { id: "all", label: "All", icon: Layers },
    { id: "movie", label: "Movies", icon: Film },
    { id: "tv", label: "TV Shows", icon: Tv },
  ];

  // Format results for DiscoveryGrid
  const formatResults = () => {
    if (!searchResults?.results) return [];

    return searchResults.results.map(item => {
      // For multi search, we need to normalize the data structure
      if (item.media_type === "movie") {
        return {
          ...item,
          title: item.title,
          release_date: item.release_date,
        };
      } else if (item.media_type === "tv") {
        return {
          ...item,
          name: item.name,
          first_air_date: item.first_air_date,
        };
      }
      return item;
    });
  };

  return (
    <>
      {/* Search Form */}
      <form onSubmit={handleSearch} className="relative mb-6">
        <Input
          type="text"
          placeholder="Search for movies, TV shows..."
          className="w-full bg-black/30 border-gray-700 text-text-light focus:border-primary-red rounded-lg pl-10 h-12"
          value={searchInput}
          onChange={(e) => setSearchInput(e.target.value)}
        />
        <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
        <Button
          type="submit"
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 rounded-md bg-primary-red hover:bg-primary-red/90 text-white"
        >
          Search
        </Button>
      </form>

      {/* Filter Buttons */}
      <div className="flex flex-wrap gap-2">
        {filterButtons.map((button) => {
          const Icon = button.icon;
          const isActive = searchType === button.id;

          return (
            <Button
              key={button.id}
              variant={isActive ? "default" : "outline"}
              className={`rounded-full ${
                isActive
                  ? "bg-primary-red hover:bg-primary-red/90 text-white"
                  : "border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white"
              }`}
              onClick={() => setSearchType(button.id)}
            >
              <Icon className={`h-4 w-4 mr-2 ${isActive ? "text-white" : ""}`} />
              {button.label}
            </Button>
          );
        })}
      </div>

      {/* Search Results will be displayed outside the search header */}
    </>
  );
}
