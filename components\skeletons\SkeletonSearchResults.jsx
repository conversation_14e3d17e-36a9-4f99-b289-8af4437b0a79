import { Skeleton } from "@/components/ui/skeleton";
import SkeletonCard from "./SkeletonCard";

function SkeletonSearchResults() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Search header */}
      <div className="mb-8">
        <Skeleton className="h-8 w-64 mb-4" />
        <div className="flex gap-3 mb-6">
          <Skeleton className="h-10 w-32 rounded-full" />
          <Skeleton className="h-10 w-32 rounded-full" />
          <Skeleton className="h-10 w-32 rounded-full" />
        </div>
      </div>
      
      {/* Results grid */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
        {Array.from({ length: 12 }).map((_, index) => (
          <SkeletonCard key={index} />
        ))}
      </div>
      
      {/* Load more */}
      <div className="w-full flex justify-center py-8">
        <Skeleton className="h-10 w-32 rounded-md" />
      </div>
    </div>
  );
}

export default SkeletonSearchResults;
