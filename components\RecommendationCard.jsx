import React from 'react';
import Link from 'next/link';

function RecommendationCard({ item }) {
  return (
    <Link href={`/movie/${item.id}`}>
      <div className='relative group overflow-hidden rounded-md cursor-pointer transition-all duration-300 hover:scale-105'>
        {/* Poster Image */}
        <div className='aspect-[2/3] bg-zinc-800'>
          {item.poster_path ? (
            <img 
              src={`https://image.tmdb.org/t/p/w300${item.poster_path}`} 
              alt={item.title}
              className='w-full h-full object-cover'
            />
          ) : (
            <div className='w-full h-full flex items-center justify-center text-zinc-500 text-sm'>
              No Image
            </div>
          )}
        </div>
        
        {/* Gradient Overlay */}
        <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300' />
        
        {/* Title */}
        <div className='absolute bottom-0 left-0 right-0 p-2 transform translate-y-full group-hover:translate-y-0 transition-transform duration-300'>
          <h3 className='text-xs font-medium truncate'>{item.title}</h3>
          <div className='flex items-center'>
            <span className='text-xs text-yellow-500'>{item.vote_average ? item.vote_average.toFixed(1) : 'N/A'}</span>
          </div>
        </div>
      </div>
    </Link>
  );
}

export default RecommendationCard;