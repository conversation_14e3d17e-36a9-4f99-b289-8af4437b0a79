
import { Link } from "react-router-dom";
import { TMDBMovie, TMDBTVShow, getImageUrl } from "@/lib/api";
import { Star, Play, Calendar } from "lucide-react";
import { motion } from "motion/react";

type MediaCardProps = {
  media: TMDBMovie | TMDBTVShow;
  type: "movie" | "tv";
};

// Determine if the media is a movie or TV show
const isMovie = (media: TMDBMovie | TMDBTVShow): media is TMDBMovie => {
  return 'title' in media;
};

const MediaCard = ({ media, type }: MediaCardProps) => {
  // Get the title/name based on media type
  const title = isMovie(media) ? media.title : media.name;
  
  // Get the release date based on media type
  const releaseDate = isMovie(media) 
    ? media.release_date 
    : media.first_air_date;
  
  // Format the date (if available)
  const formattedDate = releaseDate 
    ? new Date(releaseDate).getFullYear() 
    : "Unknown";
  
  // Generate the link path based on media type
  const linkPath = `/${type}/${media.id}`;

  return (
    <Link 
      to={linkPath} 
      className="block h-full overflow-hidden rounded-xl bg-card-dark border border-gray-800/50 hover:border-gray-700/80 transition-all duration-300 hover:shadow-xl hover:shadow-black/30 group"
    >
      <div className="relative aspect-[2/3] overflow-hidden rounded-t-xl">
        {/* Poster Image */}
        <img
          src={getImageUrl(media.poster_path, "w500")}
          alt={title}
          className="h-full w-full object-cover transform transition-transform duration-500 group-hover:scale-110"
          loading="lazy"
        />
        
        {/* Rating Badge */}
        {media.vote_average && (
          <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1 flex items-center">
            <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
            <span className="text-xs font-medium">
              {media.vote_average.toFixed(1)}
            </span>
          </div>
        )}
        
        {/* Play Button Overlay */}
        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <motion.div 
            className="bg-primary-red rounded-full p-3 text-white"
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Play className="h-6 w-6 fill-white" />
          </motion.div>
        </div>
        
        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
      
      {/* Content */}
      <div className="p-4">
        <h3 className="text-base font-semibold line-clamp-1 group-hover:text-primary-red transition-colors duration-300">{title}</h3>
        <div className="flex items-center text-gray-400 mt-2 text-sm">
          <Calendar className="h-3.5 w-3.5 mr-1.5" />
          <span>{formattedDate}</span>
          
          {/* Media Type Badge */}
          <span className={`ml-auto text-xs px-2 py-0.5 rounded-full ${type === "movie" ? "bg-primary-red/10 text-primary-red" : "bg-primary-purple/10 text-primary-purple"}`}>
            {type === "movie" ? "Movie" : "TV"}
          </span>
        </div>
      </div>
    </Link>
  );
};

export default MediaCard;
