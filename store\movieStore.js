import { create } from "zustand";
import { getTrendingMovies, getPopularMovies, getMovieDetails } from "@/lib/actions";

const useMovieStore = create((set, get) => ({
    trendingMoviesData: null,
    trendingMoviesLoading: false,
    trendingMoviesPage: 1,
    trendingMoviesError: null,
    trendingMoviesFetching: false,

    popularMoviesData: null,
    popularMoviesLoading: false,
    popularMoviesPage: 1,
    popularMoviesError: null,
    popularMoviesFetching: false,

    movieDetailsData: null,
    movieDetailsLoading: false,
    movieDetailsError: null,
    movieDetailsFetching: false,

    fetchTrendingMovies: async () => {
        set({ trendingMoviesLoading: true, trendingMoviesError: null });
        try {
            const data = await getTrendingMovies(1);
            set({
                trendingMoviesData: data,
                trendingMoviesLoading: false,
                trendingMoviesPage: 1,
            });
        } catch (error) {
            console.error("Error fetching trending movies:", error);
            set({ trendingMoviesLoading: false, trendingMoviesError: error });
        }
    },

    fetchPopularMovies: async () => {
        set({ popularMoviesLoading: true, popularMoviesError: null });
        try {
            const data = await getPopularMovies(1);
            set({
                popularMoviesData: data,
                popularMoviesLoading: false,
                popularMoviesPage: 1,
            });
        } catch (error) {
            console.error("Error fetching popular movies:", error);
            set({ popularMoviesLoading: false, popularMoviesError: error });
        }
    },

    fetchMovieDetails: async (id) => {
        set({ movieDetailsLoading: true, movieDetailsError: null });
        try {
            const data = await getMovieDetails(id);
            set({
                movieDetailsData: data,
                movieDetailsLoading: false,
            });
        } catch (error) {
            console.error("Error fetching movie details:", error);
            set({ movieDetailsLoading: false, movieDetailsError: error });
        }
    },
}));

export default useMovieStore;