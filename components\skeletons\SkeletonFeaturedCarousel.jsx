import { Skeleton } from "@/components/ui/skeleton";

function SkeletonFeaturedCarousel() {
  return (
    <div className="w-full relative">
      <div className="relative w-full h-[70vh] overflow-hidden rounded-lg bg-zinc-900/70">
        {/* Backdrop skeleton */}
        <Skeleton className="absolute inset-0" />
        
        {/* Content skeleton */}
        <div className="absolute inset-0 flex flex-col justify-end p-8 md:p-12">
          <div className="max-w-3xl space-y-4">
            <Skeleton className="h-12 w-3/4 md:h-16" />
            
            <div className="flex items-center gap-4 mb-4">
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-20" />
            </div>
            
            <Skeleton className="h-4 w-full mb-1" />
            <Skeleton className="h-4 w-full mb-1" />
            <Skeleton className="h-4 w-3/4" />
            
            <Skeleton className="h-10 w-36 mt-4" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default SkeletonFeaturedCarousel;
