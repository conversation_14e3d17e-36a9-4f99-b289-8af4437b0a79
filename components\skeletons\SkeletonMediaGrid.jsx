import { Skeleton } from "@/components/ui/skeleton";
import SkeletonCard from "./SkeletonCard";

function SkeletonMediaGrid({ title = "Loading..." }) {
  return (
    <section className="md:px-8 lg:px-12 xl:px-16">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl md:text-3xl font-bold text-gray-400">{title}</h2>
        <Skeleton className="h-8 w-24 rounded-full" />
      </div>
      
      <div className="relative">
        <div className="flex space-x-4 overflow-hidden">
          {Array.from({ length: 7 }).map((_, index) => (
            <div key={index} className="flex-none w-[180px] md:w-[200px]">
              <SkeletonCard />
            </div>
          ))}
        </div>
        
        {/* Carousel navigation skeletons */}
        <div className="absolute -left-4 top-1/2 -translate-y-1/2">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
        <div className="absolute -right-4 top-1/2 -translate-y-1/2">
          <Skeleton className="h-10 w-10 rounded-full" />
        </div>
      </div>
    </section>
  );
}

export default SkeletonMediaGrid;
