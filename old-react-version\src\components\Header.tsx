
import { useState, useEffect } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { Search, Menu, X, Film, Tv, Home, TrendingUp, Heart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { motion, AnimatePresence } from "motion/react";

// Create a HeaderContent component that uses router hooks
// This will be rendered inside the BrowserRouter context
const HeaderContent = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState("");
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  
  // Handle scroll event to change header background
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  
  // Close mobile menu when location changes
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location]);

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  // Navigation items
  const navItems = [
    { path: "/", label: "Home", icon: Home },
    { path: "/movies", label: "Movies", icon: Film },
    { path: "/tv-shows", label: "TV Shows", icon: Tv },
    { path: "/trending", label: "Trending", icon: TrendingUp },
    { path: "/favorites", label: "My List", icon: Heart },
  ];

  return (
    <motion.header 
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 100, damping: 20 }}
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled 
          ? "bg-background-dark/80 backdrop-blur-md shadow-lg border-b border-gray-800/50" 
          : "bg-gradient-to-b from-background-dark via-background-dark/80 to-transparent"
      }`}
    >
      <div className="container mx-auto px-4 py-4 flex items-center justify-between">
        {/* Logo */}
        <Link to="/" className="flex items-center z-10 group">
          <motion.h1 
            className="text-3xl font-bold text-primary-red tracking-tight group-hover:text-white transition-colors duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            RithFlix
          </motion.h1>
        </Link>
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          {navItems.map((item) => {
            const isActive = location.pathname === item.path;
            const Icon = item.icon;
            
            return (
              <Link 
                key={item.path}
                to={item.path} 
                className={`flex items-center gap-2 font-medium px-3 py-2 rounded-full transition-all duration-300 ${
                  isActive 
                    ? 'text-white bg-primary-red/20 border border-primary-red/30' 
                    : 'text-text-light hover:text-white hover:bg-gray-800/50'
                }`}
              >
                <Icon className={`h-4 w-4 ${isActive ? 'text-primary-red' : ''}`} />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </nav>
        
        {/* Desktop Search Form */}
        <div className="hidden md:flex items-center space-x-4">
          <form onSubmit={handleSearch} className="relative group">
            <Input
              type="text"
              placeholder="Search movies & TV shows..."
              className="w-64 bg-black/30 border-gray-700 text-text-light focus:border-primary-red rounded-full pl-10 pr-20 h-10 transition-all duration-300 group-hover:bg-black/50 focus:ring-2 focus:ring-primary-red/20"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 group-hover:text-primary-red transition-colors duration-300" />
            <Button 
              type="submit" 
              size="sm" 
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 rounded-full bg-primary-red hover:bg-primary-red/90 text-white shadow-md shadow-primary-red/20 hover:shadow-primary-red/40 transition-all duration-300"
            >
              Search
            </Button>
          </form>
        </div>
        
        {/* Mobile Menu Button */}
        <div className="flex md:hidden items-center space-x-3 z-10">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate('/search')}
            className="text-white hover:bg-gray-800/50 rounded-full"
          >
            <Search className="h-5 w-5" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="icon"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="text-white hover:bg-gray-800/50 rounded-full"
          >
            {mobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
        
        {/* Mobile Menu Overlay */}
        <AnimatePresence>
          {mobileMenuOpen && (
            <motion.div 
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-background-dark/95 backdrop-blur-md flex flex-col justify-center items-center z-40"
            >
              <motion.nav 
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.1 }}
                className="flex flex-col items-center space-y-6 text-lg"
              >
                {navItems.map((item) => {
                  const isActive = location.pathname === item.path;
                  const Icon = item.icon;
                  
                  return (
                    <motion.div
                      key={item.path}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link 
                        to={item.path} 
                        className={`flex items-center gap-3 font-medium px-6 py-3 rounded-full ${isActive ? 'text-primary-red bg-primary-red/10' : 'text-text-light'}`}
                      >
                        <Icon className={`h-5 w-5 ${isActive ? 'text-primary-red' : ''}`} />
                        <span>{item.label}</span>
                      </Link>
                    </motion.div>
                  );
                })}
                
                <motion.div 
                  className="mt-8 w-full max-w-xs"
                  initial={{ y: 20, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{ delay: 0.3 }}
                >
                  <form onSubmit={handleSearch} className="relative">
                    <Input
                      type="text"
                      placeholder="Search..."
                      className="w-full bg-black/30 border-gray-700 text-text-light focus:border-primary-red rounded-full pl-10 h-12"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                    <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <Button 
                      type="submit" 
                      className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 rounded-full bg-primary-red hover:bg-primary-red/90 text-white"
                    >
                      Search
                    </Button>
                  </form>
                </motion.div>
              </motion.nav>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.header>
  );
};

// Create a wrapper component that conditionally renders HeaderContent
// This component can safely be used outside Router context
const Header = () => {
  // Check if we're in a browser environment where window is available
  const isBrowser = typeof window !== 'undefined';
  
  // This will prevent the component from trying to use Router hooks during SSR
  if (!isBrowser) {
    return null;
  }
  
  // We need to check if we're inside a Router context by checking the URL
  // This is a simple hack to detect if we're on a route page
  const isInsideRouter = document.location.pathname !== undefined;
  
  if (isInsideRouter) {
    return <HeaderContent />;
  }
  
  // Fallback for when not in Router context
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-background-dark/95">
      <div className="container mx-auto px-4 py-4 flex items-center">
        <h1 className="text-3xl font-bold text-primary-red">Reelicious</h1>
      </div>
    </header>
  );
};

export default Header;
