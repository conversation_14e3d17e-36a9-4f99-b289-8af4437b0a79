'use client'

import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui/skeleton'

// Dynamically import heavy video player component
const VideoPlayer = dynamic(
  () => import('@/components/VideoPlayer'),
  {
    loading: () => (
      <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center">
        <Skeleton className="h-full w-full rounded-lg" />
      </div>
    ),
    ssr: false // Disable SSR for video player
  }
)

export default function MoviePlayer({ movieId, servers }) {
  // Component logic
  return (
    <div className="container mx-auto px-4 py-6">
      <VideoPlayer movieId={movieId} servers={servers} />
    </div>
  )
}