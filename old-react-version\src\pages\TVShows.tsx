
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import Header from "@/components/Header";
import MediaGrid from "@/components/MediaGrid";

const TVShows = () => {
  // State for pagination
  const [popularTVPage, setPopularTVPage] = useState(1);

  // Fetch popular TV shows
  const {
    data: popularTVData,
    isLoading: popularTVLoading,
    error: popularTVError,
    isFetching: popularTVFetching
  } = useQuery({
    queryKey: ["popularTV", popularTVPage],
    queryFn: () => api.getPopularTVShows(popularTVPage),
    placeholderData: previousData => previousData,
  });

  // Load more function
  const loadMorePopularTV = () => {
    if (popularTVData && popularTVPage < popularTVData.total_pages) {
      setPopularTVPage(prev => prev + 1);
    }
  };

  return (
    <div className="min-h-screen bg-background-dark">
      <Header />
      
      <main className="container mx-auto px-4 pt-24 pb-12">
        <h1 className="text-4xl font-bold mb-6">TV Shows</h1>
        <p className="text-gray-400 mb-8">
          Explore our collection of TV series
        </p>
        
        {/* Popular TV Shows Section */}
        <MediaGrid
          title="Popular TV Shows"
          data={popularTVData}
          type="tv"
          loading={popularTVLoading || popularTVFetching}
          error={popularTVError}
          onLoadMore={loadMorePopularTV}
          hasMorePages={
            popularTVData ? 
            popularTVPage < popularTVData.total_pages : 
            false
          }
        />
      </main>
      
      {/* Footer */}
      <footer className="py-6 border-t border-gray-800">
        <div className="container mx-auto px-4 text-center text-gray-400">
          <p>© 2023 Reelicious. All rights reserved.</p>
          <p className="text-sm mt-2">
            Powered by TMDB API. Not affiliated with any streaming service.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default TVShows;
