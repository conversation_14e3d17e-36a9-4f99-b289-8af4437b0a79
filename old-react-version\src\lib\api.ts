// TMDB API key and base URL
// const TMDB_API_KEY = import.meta.env.VITE_TMDB_API_KEY; // Updated API key
const TMDB_BASE_URL = "https://api.themoviedb.org/3";
const TMDB_IMAGE_BASE_URL = "https://image.tmdb.org/t/p";

const options = {
  method: "GET",
  headers: {
    accept: "application/json",
    Authorization:
      "Bearer eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJjNDM2NGRlMzgxYjI2YzcyNDZjODA4NWUyMzUzNmE4NSIsIm5iZiI6MTcyNDU2OTUxNy42NDEsInN1YiI6IjY2Y2FkN2FkZGY3NGFkZmRhMTE4ODFlZSIsInNjb3BlcyI6WyJhcGlfcmVhZCJdLCJ2ZXJzaW9uIjoxfQ.kv_QqRceqN1kSIWMredDz7a3H9arneplAEeaaR4K3p4",
  },
};

// Video player source URLs
export const VIDEO_SOURCES = {
  movie: {
    "server 1": (id: number) =>
      `https://player.autoembed.cc/embed/movie/${id}?server=2`,
    "server 2": (id: number) =>
      `https://rivestream.org/embed?type=movie&id=${id}`,
    "server 3": (id: number) => `https://multiembed.mov/?video_id=${id}&tmdb=1`,
    "server 4": (id: number) => `https://vidjoy.pro/embed/movie/${id}`,
    "server 5": (id: number) => `https://vidsrc.to/embed/movie/${id}`,
    "server 6": (id: number) => `https://www.2embed.cc/embed/${id}`,
    "server 7": (id: number) => `https://vidstream.site/embed/movie/${id}`,
  },
  tv: {
    "server 1": (id: number, season: number, episode: number) =>
      `https://player.autoembed.cc/embed/tv/${id}/${season}/${episode}?server=2`,
    "server 2": (id: number, season: number, episode: number) =>
      `https://rivestream.org/embed?type=tv&id=${id}&season=${season}&episode=${episode}`,
    "server 3": (id: number, season: number, episode: number) =>
      `https://multiembed.mov/?video_id=${id}&tmdb=1&s=${season}&e=${episode}`,
    "server 4": (id: number, season: number, episode: number) =>
      `https://vidjoy.pro/embed/tv/${id}/${season}/${episode}`,
    "server 5": (id: number, season: number, episode: number) =>
      `https://vidsrc.to/embed/tv/${id}/${season}/${episode}`,
    "server 6": (id: number, season: number, episode: number) =>
      `https://www.2embed.cc/embedtv/${id}&s=${season}&e=${episode}`,
    "server 7": (id: number, season: number, episode: number) =>
      `https://vidstream.site/embed/tv/${id}/${season}/${episode}`,
  },
};

// Image URL builder function
export const getImageUrl = (
  path: string | null,
  size: string = "original"
): string => {
  if (!path) return "/placeholder.svg";
  return `${TMDB_IMAGE_BASE_URL}/${size}${path}`;
};

// API functions for different data requirements
export const api = {
  // Movies
  getTrendingMovies: async (page: number = 1) => {
    const url = new URL(`${TMDB_BASE_URL}/trending/movie/day`);
    url.searchParams.append("page", String(page));
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },

  getPopularMovies: async (page: number = 1) => {
    const url = new URL(`${TMDB_BASE_URL}/movie/popular`);
    url.searchParams.append("page", String(page));
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },
  getMovieDetails: async (id: number) => {
    const url = new URL(`${TMDB_BASE_URL}/movie/${id}`);
    url.searchParams.append("append_to_response", "credits,videos,images");
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },

  getMovieTitleImage: async (id: number) => {
    const url = new URL(`${TMDB_BASE_URL}/movie/${id}/images`);
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },
  // TV Shows
  getTrendingTVShows: async (page: number = 1) => {
    const url = new URL(`${TMDB_BASE_URL}/trending/tv/day`);
    url.searchParams.append("page", String(page));
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },

  getPopularTVShows: async (page: number = 1) => {
    const url = new URL(`${TMDB_BASE_URL}/tv/popular`);
    url.searchParams.append("page", String(page));
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },
  getTVShowDetails: async (id: number) => {
    const url = new URL(`${TMDB_BASE_URL}/tv/${id}`);
    url.searchParams.append("append_to_response", "credits,videos,images");
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },

  getTVSeasonDetails: async (tvId: number, seasonNumber: number) => {
    const url = new URL(`${TMDB_BASE_URL}/tv/${tvId}/season/${seasonNumber}`);
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },

  getTVShowTitleImage: async (id: number) => {
    const url = new URL(`${TMDB_BASE_URL}/tv/${id}/images`);
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },
  // Search
  searchMulti: async (query: string, page: number = 1) => {
    const url = new URL(`${TMDB_BASE_URL}/search/multi`);
    url.searchParams.append("query", query);
    url.searchParams.append("page", String(page));
    const response = await fetch(url.toString(), options);
    if (!response.ok) {
      throw new Error(`TMDB API Error: ${response.status}`);
    }
    return await response.json();
  },
};

// Types for TMDB API responses
export interface TMDBMovie {
  id: number;
  title: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  release_date: string;
  vote_average: number;
  vote_count: number;
  genre_ids?: number[];
  genres?: { id: number; name: string }[];
  runtime?: number;
}

export interface TMDBTVShow {
  id: number;
  name: string;
  poster_path: string | null;
  backdrop_path: string | null;
  overview: string;
  first_air_date: string;
  vote_average: number;
  vote_count: number;
  genre_ids?: number[];
  genres?: { id: number; name: string }[];
  number_of_seasons?: number;
  number_of_episodes?: number;
  seasons?: TMDBSeason[];
}

export interface TMDBSeason {
  id: number;
  name: string;
  season_number: number;
  episode_count: number;
  poster_path: string | null;
  overview: string;
}

export interface TMDBEpisode {
  id: number;
  name: string;
  episode_number: number;
  season_number: number;
  still_path: string | null;
  overview: string;
  air_date: string;
  runtime?: number;
}

export interface TMDBPaginatedResponse<T> {
  page: number;
  results: T[];
  total_pages: number;
  total_results: number;
}

export interface TMDBSearchResult {
  id: number;
  media_type: "movie" | "tv" | "person";
  title?: string; // For movies
  name?: string; // For TV shows or persons
  poster_path: string | null;
  profile_path?: string | null; // For persons
  release_date?: string; // For movies
  first_air_date?: string; // For TV shows
}

