import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const VIDEO_SOURCES = {
  movie: {
    "server 1": (id) =>
      `https://player.autoembed.cc/embed/movie/${id}?server=2`,
    "server 2": (id) =>
      `https://rivestream.org/embed?type=movie&id=${id}`,
    "server 3": (id) => `https://multiembed.mov/?video_id=${id}&tmdb=1`,
    "server 4": (id) => `https://vidjoy.pro/embed/movie/${id}`,
    "server 5": (id) => `https://vidsrc.to/embed/movie/${id}`,
    "server 6": (id) => `https://www.2embed.cc/embed/${id}`,
    "server 7": (id) => `https://vidstream.site/embed/movie/${id}`,
  },
  tv: {
    "server 1": (id, season, episode) =>
      `https://player.autoembed.cc/embed/tv/${id}/${season}/${episode}?server=2`,
    "server 2": (id, season, episode) =>
      `https://rivestream.org/embed?type=tv&id=${id}&season=${season}&episode=${episode}`,
    "server 3": (id, season, episode) =>
      `https://multiembed.mov/?video_id=${id}&tmdb=1&s=${season}&e=${episode}`,
    "server 4": (id, season, episode) =>
      `https://vidjoy.pro/embed/tv/${id}/${season}/${episode}`,
    "server 5": (id, season, episode) =>
      `https://vidsrc.to/embed/tv/${id}/${season}/${episode}`,
    "server 6": (id, season, episode) =>
      `https://www.2embed.cc/embedtv/${id}&s=${season}&e=${episode}`,
    "server 7": (id, season, episode) =>
      `https://vidstream.site/embed/tv/${id}/${season}/${episode}`,
  },
};
