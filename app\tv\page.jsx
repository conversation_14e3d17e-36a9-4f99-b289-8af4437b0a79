"use client";

import { useEffect } from "react";
import { motion } from "motion/react";
import DiscoveryGrid from "@/components/DiscoveryGrid";
import FilterPanel from "@/components/FilterPanel";
import useDiscoverTVStore from "@/store/discoverTVStore";

function TVShowsPage() {
  const {
    discoverTVData,
    discoverTVLoading,
    discoverTVError,
    discoverTVHasMore,
    fetchDiscoverTV,
    loadMoreTV,
    genresList,
    fetchGenres,
    filterParams,
    setFilterParams,
    resetFilters,
  } = useDiscoverTVStore();

  useEffect(() => {
    // Fetch initial data if not already loaded
    if (!discoverTVData) {
      fetchDiscoverTV();
    }

    // Fetch genres if not already loaded
    if (!genresList) {
      fetchGenres();
    }
  }, [discoverTVData, fetchDiscoverTV, genresList, fetchGenres]);

  return (
    <main className="pt-20 pb-16">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold mb-2">Discover TV Shows</h1>
          <p className="text-gray-400">
            Explore thousands of TV shows with advanced filtering options
          </p>
        </motion.div>

        {/* Filter Panel */}
        <FilterPanel
          type="tv"
          genres={genresList}
          filterParams={filterParams}
          onFilterChange={setFilterParams}
          onResetFilters={resetFilters}
        />

        {/* TV Show Grid */}
        <DiscoveryGrid
          title="TV Shows"
          data={discoverTVData?.results || []}
          type="tv"
          loading={discoverTVLoading}
          error={discoverTVError}
          hasMore={discoverTVHasMore}
          onLoadMore={loadMoreTV}
        />
      </div>
    </main>
  );
}

export default TVShowsPage;