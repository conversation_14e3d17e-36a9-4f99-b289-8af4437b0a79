import { create } from "zustand";
import { getTrendingTVShows, getPopularTVShows, getTVShowDetails } from "@/lib/actions";

const useTVShowStore = create((set, get) => ({
  trendingTVData: null,
  trendingTVLoading: false,
  trendingTVPage: 1,
  trendingTVError: null,
  trendingTVFetching: false,

  popularTVData: null,
  popularTVLoading: false,
  popularTVPage: 1,
  popularTVError: null,
  popularTVFetching: false,

  tvShowDetailsData: null,
  tvShowDetailsLoading: false,
  tvShowDetailsError: null,
  tvShowDetailsFetching: false,

  fetchTrendingTV: async () => {
    set({ trendingTVLoading: true, trendingTVError: null });
    try {
      const data = await getTrendingTVShows(1);
      set({
        trendingTVData: data,
        trendingTVLoading: false,
        trendingTVPage: 1,
      });
    } catch (error) {
      set({ trendingTVLoading: false, trendingTVError: error });
    }
  },

  fetchPopularTV: async () => {
    set({ popularTVLoading: true, popularTVError: null });
    try {
      const data = await getPopularTVShows(1);
      set({
        popularTVData: data,
        popularTVLoading: false,
        popularTVPage: 1,
      });
    } catch (error) {
      set({ popularTVLoading: false, popularTVError: error });
    }
  },

  fetchTVShowDetails: async (id) => {
    set({ tvShowDetailsLoading: true, tvShowDetailsError: null });
    try {
      const data = await getTVShowDetails(id);
      set({
        tvShowDetailsData: data,
        tvShowDetailsLoading: false,
      });
    } catch (error) {
      set({ tvShowDetailsLoading: false, tvShowDetailsError: error });
    }
  },
}));

export default useTVShowStore;