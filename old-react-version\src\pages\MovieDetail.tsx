import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { api, getImageUrl } from "@/lib/api";
import Header from "@/components/Header";
import VideoPlayer from "@/components/VideoPlayer";
import { Button } from "@/components/ui/button";
import { Star, Clock, Calendar, ArrowLeft, Loader } from "lucide-react";

const MovieDetail = () => {
  const { id } = useParams();
  const movieId = id ? parseInt(id) : 0;

  // Fetch movie details
  const {
    data: movie,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["movie", movieId],
    queryFn: async () => {
      const [movieDetails, titleImage] = await Promise.all([
        api.getMovieDetails(movieId),
        api.getMovieTitleImage(movieId),
      ]);
      return { ...movieDetails, titleImage };
    },
    enabled: !!movieId,
  });

  const titleLogos = movie?.titleImage.logos;
  const titlelogo = titleLogos?.find((logo) => logo.iso_639_1 === "en");

  // Loading state
  if (isLoading) {
    return (
      <div className='min-h-screen bg-background-dark flex items-center justify-center'>
        <Loader className='h-12 w-12 text-primary-red animate-spin' />
      </div>
    );
  }

  // Error state
  if (error || !movie) {
    return (
      <div className='min-h-screen bg-background-dark'>
        <Header />
        <div className='container mx-auto px-4 pt-24 pb-12'>
          <div className='flex items-center mb-6'>
            <Link to='/'>
              <Button variant='ghost' size='sm'>
                <ArrowLeft className='h-4 w-4 mr-2 text-white' />
                <span className='text-white'>Back</span>
              </Button>
            </Link>
          </div>
          <div className='p-8 bg-card-dark rounded-lg text-center'>
            <h2 className='text-2xl font-bold mb-4'>Error</h2>
            <p>Unable to load movie details. Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  // Format runtime (minutes to hours and minutes)
  const formatRuntime = (minutes?: number) => {
    if (!minutes) return "Unknown";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // Format release date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Unknown";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className='min-h-screen bg-background-dark'>
      <Header />


      {/* Backdrop image - Increased height from 50vh to 70vh */}
      <div
        className='absolute top-0 left-0 right-0 h-[70vh] bg-cover bg-center bg-no-repeat opacity-30'
        style={{
          backgroundImage: `url(${getImageUrl(
            movie.backdrop_path,
            "w780"
          )})`,
        }}
      />

      <main className='relative container mx-auto px-4 pt-20 pb-12'>
        <div className='flex items-center mb-6'>
          <Link to='/'>
            <Button variant='ghost' size='sm'>
              <ArrowLeft className='h-4 w-4 mr-2 text-white' />
              <span className='text-white'>Back</span>
            </Button>
          </Link>
        </div>

        <div className='flex flex-col md:flex-row gap-8 items-start'>
          {/* Poster */}
          <div className='w-full md:w-1/3 lg:w-1/4 flex-shrink-0 animate-fade-in'>
            <img
              src={getImageUrl(movie.poster_path, "w500")}
              alt={movie.title}
              className='w-full rounded-lg shadow-lg object-cover'
            />
          </div>

          {/* Movie details */}
          <div className='flex-grow animate-slide-up'>
            {titlelogo ? (
              <img
                src={getImageUrl(titlelogo.file_path, "w500")}
                alt={movie.title}
                className='w-96 rounded-lg shadow-lg object-cover mb-6'
              />
            ) : (
              <h1 className='text-3xl md:text-4xl font-bold mb-3'>
                {movie.title}
              </h1>
            )}
            {/* <h1 className="text-3xl md:text-4xl font-bold mb-3">{movie.title}</h1> */}
            {/* Title image */}

            {/* Movie metadata */}
            <div className='flex flex-wrap items-center gap-x-6 gap-y-3 mb-6 text-gray-300'>
              {movie.vote_average ? (
                <div className='flex items-center'>
                  <Star className='h-5 w-5 text-yellow-400 fill-yellow-400 mr-1.5' />
                  <span className="font-medium">{movie.vote_average.toFixed(1)}</span><span className="text-sm text-gray-400">/10</span>
                </div>
              ) : null}

              {movie.runtime ? (
                <div className='flex items-center'>
                  <Clock className='h-5 w-5 text-gray-400 mr-1.5' />
                  <span>{formatRuntime(movie.runtime)}</span>
                </div>
              ) : null}

              {movie.release_date ? (
                <div className='flex items-center'>
                  <Calendar className='h-5 w-5 text-gray-400 mr-1.5' />
                  <span>{formatDate(movie.release_date)}</span>
                </div>
              ) : null}
            </div>

            {/* Genres */}
            {movie.genres && movie.genres.length > 0 && (
              <div className='flex flex-wrap gap-2 mb-6'>
                {movie.genres.map((genre) => (
                  <span
                    key={genre.id}
                    className='px-3 py-1.5 bg-gray-700/50 hover:bg-gray-600/70 rounded-full text-xs font-medium text-gray-300 transition-colors duration-150'
                  >
                    {genre.name}
                  </span>
                ))}
              </div>
            )}

            {/* Overview */}
            <div className='mb-8'>
              <h2 className='text-2xl font-semibold mb-3 text-white'>Overview</h2>
              <p className='text-gray-300 leading-relaxed text-base'>
                {movie.overview || "No overview available."}
              </p>
            </div>
          </div>
        </div>

        {/* Video player - Moved to next line */}
        <div className='mt-8'>
          <h2 className='text-xl font-semibold mb-4 text-white'>Watch Now</h2>
          <VideoPlayer tmdbId={movieId} type='movie' />
        </div>
      </main>

      {/* Footer */}
      <footer className='py-6 border-t border-gray-800'>
        <div className='container mx-auto px-4 text-center text-gray-400'>
          <p>© 2023 Reelicious. All rights reserved.</p>
          <p className='text-sm mt-2'>
            Powered by TMDB API. Not affiliated with any streaming service.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default MovieDetail;
