"use client";

import { useState } from "react";
import { motion } from "motion/react";
import {
  Filter,
  SlidersHorizontal,
  X,
  ChevronDown,
  ChevronUp
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";

function FilterPanel({
  type,
  genres,
  filterParams,
  onFilterChange,
  onResetFilters
}) {
  const [isOpen, setIsOpen] = useState(false);

  const togglePanel = () => {
    setIsOpen(!isOpen);
  };

  const handleSortChange = (value) => {
    onFilterChange({ sort_by: value });
  };

  const handleGenreChange = (value) => {
    onFilterChange({ with_genres: value });
  };

  const handleYearChange = (e) => {
    const value = e.target.value;
    if (type === "movie") {
      onFilterChange({ primary_release_year: value });
    } else {
      onFilterChange({ first_air_date_year: value });
    }
  };

  const handleRatingChange = (value) => {
    onFilterChange({ vote_average_gte: value });
  };

  const handleLanguageChange = (value) => {
    onFilterChange({ with_original_language: value });
  };

  // Get active filter count
  const getActiveFilterCount = () => {
    let count = 0;
    if (filterParams.with_genres && filterParams.with_genres !== 'all_genres') count++;
    if (filterParams.primary_release_year || filterParams.first_air_date_year) count++;
    if (filterParams.vote_average_gte && filterParams.vote_average_gte !== 'any_rating') count++;
    if (filterParams.with_original_language && filterParams.with_original_language !== 'any_language') count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="outline"
          className="flex items-center gap-2 bg-zinc-900/50 border-gray-700 hover:bg-zinc-800 hover:text-white"
          onClick={togglePanel}
        >
          <Filter className="h-4 w-4" />
          <span>Filters</span>
          {activeFilterCount > 0 && (
            <Badge variant="default" className="ml-1 bg-primary-red text-white">
              {activeFilterCount}
            </Badge>
          )}
          {isOpen ? (
            <ChevronUp className="h-4 w-4 ml-1" />
          ) : (
            <ChevronDown className="h-4 w-4 ml-1" />
          )}
        </Button>

        <Select
          value={filterParams.sort_by}
          onValueChange={handleSortChange}
        >
          <SelectTrigger className="w-[180px] bg-zinc-900/50 border-gray-700">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent className="bg-zinc-900 border-gray-700">
            <SelectItem value="popularity.desc">Popularity ↓</SelectItem>
            <SelectItem value="popularity.asc">Popularity ↑</SelectItem>
            <SelectItem value="vote_average.desc">Rating ↓</SelectItem>
            <SelectItem value="vote_average.asc">Rating ↑</SelectItem>
            <SelectItem value="primary_release_date.desc">Release Date ↓</SelectItem>
            <SelectItem value="primary_release_date.asc">Release Date ↑</SelectItem>
            <SelectItem value="title.asc">Title (A-Z)</SelectItem>
            <SelectItem value="title.desc">Title (Z-A)</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-zinc-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 mb-6"
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold flex items-center">
              <SlidersHorizontal className="h-5 w-5 mr-2" />
              Filter Options
            </h3>
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onResetFilters}
                className="text-gray-400 hover:text-white"
              >
                <X className="h-4 w-4 mr-1" />
                Reset Filters
              </Button>
            )}
          </div>

          <Separator className="mb-4 bg-gray-700/30" />

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Genres Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Genres</label>
              <Select
                value={filterParams.with_genres}
                onValueChange={handleGenreChange}
              >
                <SelectTrigger className="w-full bg-zinc-800 border-gray-700">
                  <SelectValue placeholder="Select genre" />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-gray-700 max-h-[300px]">
                  <SelectItem value="all_genres">All Genres</SelectItem>
                  {genres && genres.map((genre) => (
                    <SelectItem key={genre.id} value={genre.id.toString()}>
                      {genre.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Year Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {type === "movie" ? "Release Year" : "First Air Date Year"}
              </label>
              <Input
                type="number"
                placeholder="Enter year"
                className="bg-zinc-800 border-gray-700"
                value={type === "movie" ? filterParams.primary_release_year : filterParams.first_air_date_year}
                onChange={handleYearChange}
                min="1900"
                max={new Date().getFullYear()}
              />
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Minimum Rating</label>
              <Select
                value={filterParams.vote_average_gte}
                onValueChange={handleRatingChange}
              >
                <SelectTrigger className="w-full bg-zinc-800 border-gray-700">
                  <SelectValue placeholder="Select minimum rating" />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-gray-700">
                  <SelectItem value="any_rating">Any Rating</SelectItem>
                  <SelectItem value="9">9+ ★</SelectItem>
                  <SelectItem value="8">8+ ★</SelectItem>
                  <SelectItem value="7">7+ ★</SelectItem>
                  <SelectItem value="6">6+ ★</SelectItem>
                  <SelectItem value="5">5+ ★</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Language Filter */}
            <div>
              <label className="block text-sm font-medium mb-2">Language</label>
              <Select
                value={filterParams.with_original_language}
                onValueChange={handleLanguageChange}
              >
                <SelectTrigger className="w-full bg-zinc-800 border-gray-700">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent className="bg-zinc-800 border-gray-700">
                  <SelectItem value="any_language">Any Language</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="fr">French</SelectItem>
                  <SelectItem value="de">German</SelectItem>
                  <SelectItem value="es">Spanish</SelectItem>
                  <SelectItem value="ja">Japanese</SelectItem>
                  <SelectItem value="ko">Korean</SelectItem>
                  <SelectItem value="hi">Hindi</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
}

export default FilterPanel;
