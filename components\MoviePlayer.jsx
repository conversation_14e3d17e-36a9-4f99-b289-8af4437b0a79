import React, { useState } from "react";
import Iframe from "react-iframe";
import useMovieStore from "@/store/movieStore";
import { Play, Maximize, Volume2, Settings, Server, ChevronDown } from "lucide-react";
import { Button } from "@/components/ui/button";
import { getImageUrl } from "@/lib/image";
import { VIDEO_SOURCES } from "@/lib/utils";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

function MoviePlayer() {
  const { movieDetailsData } = useMovieStore();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentServer, setCurrentServer] = useState("server 1");

  if (!movieDetailsData) {
    return (
      <div className='w-full aspect-video bg-zinc-900 flex items-center justify-center'>
        <div className="w-12 h-12 border-4 border-primary-red border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handleServerChange = (server) => {
    setCurrentServer(server);
    // If already playing, no need to set isPlaying again
    if (!isPlaying) {
      setIsPlaying(true);
    }
  };

  // Get the video source URL based on the selected server
  const getVideoSourceUrl = () => {
    const serverFunction = VIDEO_SOURCES.movie[currentServer];
    if (serverFunction) {
      return serverFunction(movieDetailsData.id);
    }
    // Fallback to server 1 if the selected server doesn't exist
    return VIDEO_SOURCES.movie["server 1"](movieDetailsData.id);
  };

  return (
    <div className='w-full aspect-video bg-zinc-900 overflow-hidden group relative'>
      {!isPlaying ? (
        <>
          {/* Backdrop Image */}
          {movieDetailsData.backdrop_path && (
            <div className='absolute inset-0'>
              <img
                src={getImageUrl(movieDetailsData.backdrop_path, "w780")}
                alt={movieDetailsData.title}
                className='w-full h-full object-cover'
              />
              {/* Gradient Overlay */}
              <div className='absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-black/40' />
            </div>
          )}

          {/* Play Button */}
          <div className="absolute inset-0 flex flex-col items-center justify-center z-20">
            <Button
              onClick={handlePlay}
              className="bg-primary-red hover:bg-primary-red/90 text-white rounded-full w-16 h-16 flex items-center justify-center mb-4"
            >
              <Play className="h-8 w-8 ml-1" />
            </Button>
            <h2 className="text-2xl font-bold text-white mb-2">{movieDetailsData.title}</h2>
            <p className="text-gray-300 text-sm max-w-md text-center mb-4">
              Click to play the movie
            </p>

            {/* Server Selection */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="bg-black/30 backdrop-blur-sm border-gray-700/50 hover:bg-black/50">
                  <Server className="h-4 w-4 mr-2" />
                  {currentServer.charAt(0).toUpperCase() + currentServer.slice(1)}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                {Object.keys(VIDEO_SOURCES.movie).map((server) => (
                  <DropdownMenuItem
                    key={server}
                    className={`cursor-pointer ${currentServer === server ? 'bg-primary-red/20 text-primary-red' : ''}`}
                    onClick={() => handleServerChange(server)}
                  >
                    {server.charAt(0).toUpperCase() + server.slice(1)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </>
      ) : (
        <>
          {/* Gradient Overlay */}
          <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-black/40 z-10' />

          {/* Server Selection (when playing) */}
          <div className="absolute top-4 right-4 z-30">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="bg-black/50 backdrop-blur-sm border-gray-700/50 hover:bg-black/70">
                  <Server className="h-4 w-4 mr-2" />
                  {currentServer.charAt(0).toUpperCase() + currentServer.slice(1)}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-zinc-900/95 backdrop-blur-sm border-gray-700/50">
                {Object.keys(VIDEO_SOURCES.movie).map((server) => (
                  <DropdownMenuItem
                    key={server}
                    className={`cursor-pointer ${currentServer === server ? 'bg-primary-red/20 text-primary-red' : ''}`}
                    onClick={() => handleServerChange(server)}
                  >
                    {server.charAt(0).toUpperCase() + server.slice(1)}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Movie Player */}
          <Iframe
            url={getVideoSourceUrl()}
            width='100%'
            height='100%'
            display='block'
            position='relative'
            className='z-20'
            id="movie-iframe"
            allowFullScreen
          />
        </>
      )}
    </div>
  );
}

export default MoviePlayer;
