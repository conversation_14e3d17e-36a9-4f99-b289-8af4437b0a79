"use server";

const TMDB_BASE_URL = "https://api.themoviedb.org/3";

const options = {
  method: "GET",
  headers: {
    accept: "application/json",
    Authorization: `Bearer ${process.env.API_KEY}`,
  },
};

export async function getTrendingMovies(page = 1) {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/trending/movie/day?page=${page}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getTrendingTVShows(page = 1) {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/trending/tv/day?page=${page}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getPopularMovies(page = 1) {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/movie/popular?page=${page}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getPopularTVShows(page = 1) {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/tv/popular?page=${page}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getMovieDetails(id) {
  try {
    // Fetch basic movie details
    const detailsResponse = await fetch(`${TMDB_BASE_URL}/movie/${id}`, options);
    const detailsData = await detailsResponse.json();

    // Fetch credits (cast and crew)
    const creditsResponse = await fetch(`${TMDB_BASE_URL}/movie/${id}/credits`, options);
    const creditsData = await creditsResponse.json();

    // Fetch recommendations
    const recommendationsResponse = await fetch(`${TMDB_BASE_URL}/movie/${id}/recommendations`, options);
    const recommendationsData = await recommendationsResponse.json();

    // Combine all data
    return {
      ...detailsData,
      credits: creditsData,
      recommendations: recommendationsData
    };
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getTVShowDetails(id) {
  try {
    // Fetch basic TV show details
    const detailsResponse = await fetch(`${TMDB_BASE_URL}/tv/${id}`, options);
    const detailsData = await detailsResponse.json();

    // Fetch credits (cast and crew)
    const creditsResponse = await fetch(`${TMDB_BASE_URL}/tv/${id}/credits`, options);
    const creditsData = await creditsResponse.json();

    // Fetch recommendations
    const recommendationsResponse = await fetch(`${TMDB_BASE_URL}/tv/${id}/recommendations`, options);
    const recommendationsData = await recommendationsResponse.json();

    // Fetch seasons
    const seasonsData = [];
    if (detailsData.seasons && detailsData.seasons.length > 0) {
      // Only fetch details for the first few seasons to avoid too many requests
      const seasonsToFetch = detailsData.seasons.slice(0, 3);
      for (const season of seasonsToFetch) {
        const seasonResponse = await fetch(
          `${TMDB_BASE_URL}/tv/${id}/season/${season.season_number}`,
          options
        );
        const seasonData = await seasonResponse.json();
        seasonsData.push(seasonData);
      }
    }

    // Combine all data
    return {
      ...detailsData,
      credits: creditsData,
      recommendations: recommendationsData,
      seasonsDetails: seasonsData
    };
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getDiscoverMovies(params = {}) {
  try {
    // Default parameters
    const defaultParams = {
      page: 1,
      sort_by: 'popularity.desc',
      include_adult: false,
      include_video: false,
      language: 'en-US',
    };

    // Merge default parameters with provided parameters
    const queryParams = new URLSearchParams({
      ...defaultParams,
      ...params,
    });

    const response = await fetch(
      `${TMDB_BASE_URL}/discover/movie?${queryParams.toString()}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getDiscoverTV(params = {}) {
  try {
    // Default parameters
    const defaultParams = {
      page: 1,
      sort_by: 'popularity.desc',
      include_adult: false,
      language: 'en-US',
    };

    // Merge default parameters with provided parameters
    const queryParams = new URLSearchParams({
      ...defaultParams,
      ...params,
    });

    const response = await fetch(
      `${TMDB_BASE_URL}/discover/tv?${queryParams.toString()}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getGenreMovieList() {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/genre/movie/list`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getGenreTVList() {
  try {
    const response = await fetch(
      `${TMDB_BASE_URL}/genre/tv/list`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function searchMulti(query, page = 1) {
  try {
    const queryParams = new URLSearchParams({
      query,
      page,
      include_adult: false,
      language: 'en-US',
    });

    const response = await fetch(
      `${TMDB_BASE_URL}/search/multi?${queryParams.toString()}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function searchMovies(query, page = 1) {
  try {
    const queryParams = new URLSearchParams({
      query,
      page,
      include_adult: false,
      language: 'en-US',
    });

    const response = await fetch(
      `${TMDB_BASE_URL}/search/movie?${queryParams.toString()}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function searchTVShows(query, page = 1) {
  try {
    const queryParams = new URLSearchParams({
      query,
      page,
      include_adult: false,
      language: 'en-US',
    });

    const response = await fetch(
      `${TMDB_BASE_URL}/search/tv?${queryParams.toString()}`,
      options
    );
    const data = await response.json();
    return data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}