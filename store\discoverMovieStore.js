import { create } from "zustand";
import { getDiscoverMovies, getGenreMovieList } from "@/lib/actions";

const useDiscoverMovieStore = create((set, get) => ({
  // Discovery data
  discoverMoviesData: null,
  discoverMoviesLoading: false,
  discoverMoviesPage: 1,
  discoverMoviesError: null,
  discoverMoviesFetching: false,
  discoverMoviesHasMore: true,

  // Filters
  genresList: null,
  genresLoading: false,
  genresError: null,

  // Filter and sort parameters
  filterParams: {
    sort_by: 'popularity.desc',
    with_genres: 'all_genres',
    primary_release_year: '',
    vote_average_gte: 'any_rating',
    with_original_language: 'any_language',
  },

  // Set filter parameters
  setFilterParams: (params) => {
    set((state) => ({
      filterParams: {
        ...state.filterParams,
        ...params,
      },
      // Reset page when filters change
      discoverMoviesPage: 1,
      discoverMoviesData: null,
      discoverMoviesHasMore: true,
    }));

    // Fetch movies with new filters
    get().fetchDiscoverMovies();
  },

  // Fetch movie genres
  fetchGenres: async () => {
    set({ genresLoading: true, genresError: null });
    try {
      const data = await getGenreMovieList();
      set({
        genresList: data.genres,
        genresLoading: false,
      });
    } catch (error) {
      console.error("Error fetching movie genres:", error);
      set({ genresLoading: false, genresError: error });
    }
  },

  // Fetch discover movies (initial load)
  fetchDiscoverMovies: async () => {
    const { discoverMoviesPage, filterParams, discoverMoviesData } = get();

    // If already fetching, don't fetch again
    if (get().discoverMoviesFetching) return;

    set({
      discoverMoviesLoading: discoverMoviesData === null,
      discoverMoviesFetching: true,
      discoverMoviesError: null
    });

    // Process filter parameters to handle special values
    const processedParams = { ...filterParams };

    // Handle special values
    if (processedParams.with_genres === 'all_genres') {
      delete processedParams.with_genres;
    }

    if (processedParams.vote_average_gte === 'any_rating') {
      delete processedParams.vote_average_gte;
    }

    if (processedParams.with_original_language === 'any_language') {
      delete processedParams.with_original_language;
    }

    try {
      const data = await getDiscoverMovies({
        page: discoverMoviesPage,
        ...processedParams,
      });

      set({
        discoverMoviesData: discoverMoviesPage === 1
          ? data
          : {
              ...data,
              results: [...(discoverMoviesData?.results || []), ...data.results],
            },
        discoverMoviesLoading: false,
        discoverMoviesFetching: false,
        discoverMoviesHasMore: data.page < data.total_pages,
      });
    } catch (error) {
      console.error("Error fetching discover movies:", error);
      set({
        discoverMoviesLoading: false,
        discoverMoviesFetching: false,
        discoverMoviesError: error
      });
    }
  },

  // Load more movies (for infinite scrolling)
  loadMoreMovies: () => {
    const { discoverMoviesHasMore, discoverMoviesFetching } = get();

    if (!discoverMoviesHasMore || discoverMoviesFetching) return;

    set((state) => ({ discoverMoviesPage: state.discoverMoviesPage + 1 }));
    get().fetchDiscoverMovies();
  },

  // Reset filters
  resetFilters: () => {
    set({
      filterParams: {
        sort_by: 'popularity.desc',
        with_genres: 'all_genres',
        primary_release_year: '',
        vote_average_gte: 'any_rating',
        with_original_language: 'any_language',
      },
      discoverMoviesPage: 1,
      discoverMoviesData: null,
      discoverMoviesHasMore: true,
    });

    get().fetchDiscoverMovies();
  },
}));

export default useDiscoverMovieStore;
