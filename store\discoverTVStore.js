import { create } from "zustand";
import { getDiscoverTV, getGenreTVList } from "@/lib/actions";

const useDiscoverTVStore = create((set, get) => ({
  // Discovery data
  discoverTVData: null,
  discoverTVLoading: false,
  discoverTVPage: 1,
  discoverTVError: null,
  discoverTVFetching: false,
  discoverTVHasMore: true,

  // Filters
  genresList: null,
  genresLoading: false,
  genresError: null,

  // Filter and sort parameters
  filterParams: {
    sort_by: 'popularity.desc',
    with_genres: 'all_genres',
    first_air_date_year: '',
    vote_average_gte: 'any_rating',
    with_original_language: 'any_language',
  },

  // Set filter parameters
  setFilterParams: (params) => {
    set((state) => ({
      filterParams: {
        ...state.filterParams,
        ...params,
      },
      // Reset page when filters change
      discoverTVPage: 1,
      discoverTVData: null,
      discoverTVHasMore: true,
    }));

    // Fetch TV shows with new filters
    get().fetchDiscoverTV();
  },

  // Fetch TV genres
  fetchGenres: async () => {
    set({ genresLoading: true, genresError: null });
    try {
      const data = await getGenreTVList();
      set({
        genresList: data.genres,
        genresLoading: false,
      });
    } catch (error) {
      console.error("Error fetching TV genres:", error);
      set({ genresLoading: false, genresError: error });
    }
  },

  // Fetch discover TV shows (initial load)
  fetchDiscoverTV: async () => {
    const { discoverTVPage, filterParams, discoverTVData } = get();

    // If already fetching, don't fetch again
    if (get().discoverTVFetching) return;

    set({
      discoverTVLoading: discoverTVData === null,
      discoverTVFetching: true,
      discoverTVError: null
    });

    // Process filter parameters to handle special values
    const processedParams = { ...filterParams };

    // Handle special values
    if (processedParams.with_genres === 'all_genres') {
      delete processedParams.with_genres;
    }

    if (processedParams.vote_average_gte === 'any_rating') {
      delete processedParams.vote_average_gte;
    }

    if (processedParams.with_original_language === 'any_language') {
      delete processedParams.with_original_language;
    }

    try {
      const data = await getDiscoverTV({
        page: discoverTVPage,
        ...processedParams,
      });

      set({
        discoverTVData: discoverTVPage === 1
          ? data
          : {
              ...data,
              results: [...(discoverTVData?.results || []), ...data.results],
            },
        discoverTVLoading: false,
        discoverTVFetching: false,
        discoverTVHasMore: data.page < data.total_pages,
      });
    } catch (error) {
      console.error("Error fetching discover TV shows:", error);
      set({
        discoverTVLoading: false,
        discoverTVFetching: false,
        discoverTVError: error
      });
    }
  },

  // Load more TV shows (for infinite scrolling)
  loadMoreTV: () => {
    const { discoverTVHasMore, discoverTVFetching } = get();

    if (!discoverTVHasMore || discoverTVFetching) return;

    set((state) => ({ discoverTVPage: state.discoverTVPage + 1 }));
    get().fetchDiscoverTV();
  },

  // Reset filters
  resetFilters: () => {
    set({
      filterParams: {
        sort_by: 'popularity.desc',
        with_genres: 'all_genres',
        first_air_date_year: '',
        vote_average_gte: 'any_rating',
        with_original_language: 'any_language',
      },
      discoverTVPage: 1,
      discoverTVData: null,
      discoverTVHasMore: true,
    });

    get().fetchDiscoverTV();
  },
}));

export default useDiscoverTVStore;