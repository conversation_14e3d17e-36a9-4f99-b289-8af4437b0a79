import { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { api, TMDBSearchResult } from "@/lib/api";
import Header from "@/components/Header";
import MediaCard from "@/components/MediaCard";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search as SearchIcon, Loader, Film, Tv } from "lucide-react";

const Search = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Get search query from URL
  const queryParams = new URLSearchParams(location.search);
  const urlQuery = queryParams.get("q") || "";
  
  // Local state for search input
  const [searchQuery, setSearchQuery] = useState(urlQuery);
  const [currentPage, setCurrentPage] = useState(1);
  
  // Update search input when URL changes
  useEffect(() => {
    setSearchQuery(urlQuery);
  }, [urlQuery]);
  
  // Perform search
  const {
    data: searchResults,
    isLoading,
    isFetching,
    error,
  } = useQuery({
    queryKey: ["search", urlQuery, currentPage],
    queryFn: () => api.searchMulti(urlQuery, currentPage),
    enabled: !!urlQuery,
    placeholderData: previousData => previousData,
  });
  
  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setCurrentPage(1);
    }
  };
  
  // Filter results by media type
  const getFilteredResults = (mediaType: 'movie' | 'tv') => {
    if (!searchResults?.results) return [];
    return searchResults.results.filter(
      (item: TMDBSearchResult) => item.media_type === mediaType
    );
  };
  
  const movieResults = getFilteredResults('movie');
  const tvResults = getFilteredResults('tv');
  
  // Load more results
  const loadMore = () => {
    if (searchResults && currentPage < searchResults.total_pages) {
      setCurrentPage(prev => prev + 1);
    }
  };

  return (
    <div className="min-h-screen bg-background-dark">
      <Header />
      
      <main className="container mx-auto px-4 pt-24 pb-12">
        <h1 className="text-3xl font-bold mb-6">Search</h1>
        
        {/* Search Form */}
        <form onSubmit={handleSearch} className="mb-8 flex gap-2">
          <Input
            type="text"
            placeholder="Search for movies or TV shows..."
            className="flex-grow bg-card-dark border-gray-700"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <Button type="submit" className="bg-primary-red hover:bg-primary-red/80">
            <SearchIcon className="h-4 w-4 mr-2" />
            Search
          </Button>
        </form>
        
        {/* Loading State */}
        {isLoading && (
          <div className="flex flex-col items-center justify-center py-12">
            <Loader className="h-12 w-12 text-primary-red animate-spin mb-4" />
            <p>Searching...</p>
          </div>
        )}
        
        {/* Error State */}
        {error && (
          <div className="p-8 bg-red-900/20 border border-red-900 rounded-lg text-center">
            <p>Error searching. Please try again later.</p>
          </div>
        )}
        
        {/* No Search Query */}
        {!urlQuery && !isLoading && (
          <div className="text-center py-16">
            <SearchIcon className="h-20 w-20 mx-auto text-gray-500 mb-6" />
            <p className="text-2xl text-gray-400">
              Enter a search term to find movies and TV shows
            </p>
          </div>
        )}
        
        {/* No Results */}
        {urlQuery && searchResults?.results.length === 0 && !isLoading && (
          <div className="text-center py-16">
            {/* Consider adding a more visually distinct 'not found' icon here if available */}
            <p className="text-2xl mb-3">No results found for "{urlQuery}"</p>
            <p className="text-gray-400 text-lg">
              Try adjusting your search terms or try another search.
            </p>
          </div>
        )}
        
        {/* Search Results */}
        {searchResults && searchResults.results.length > 0 && (
          <div className="animate-fade-in">
            <div className="mb-6">
              <p className="text-gray-300 font-medium text-lg">
                Found {searchResults.total_results} results for "{urlQuery}"
              </p>
            </div>
            
            {/* Movie Results */}
            {movieResults.length > 0 && (
              <section className="mb-8">
                <div className="flex items-center mb-4">
                  <Film className="h-5 w-5 mr-2 text-primary-red" />
                  <h2 className="text-2xl font-semibold">Movies</h2>
                </div>
                
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {movieResults.map((movie: TMDBSearchResult) => (
                    <MediaCard 
                      key={`movie-${movie.id}`} 
                      media={{
                        id: movie.id,
                        title: movie.title || "",
                        poster_path: movie.poster_path,
                        backdrop_path: null,
                        overview: "",
                        release_date: movie.release_date || "",
                        vote_average: 0,
                        vote_count: 0
                      }} 
                      type="movie" 
                    />
                  ))}
                </div>
              </section>
            )}
            
            {/* TV Show Results */}
            {tvResults.length > 0 && (
              <section className="mb-8">
                <div className="flex items-center mb-4">
                  <Tv className="h-5 w-5 mr-2 text-primary-purple" />
                  <h2 className="text-2xl font-semibold">TV Shows</h2>
                </div>
                
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                  {tvResults.map((show: TMDBSearchResult) => (
                    <MediaCard 
                      key={`tv-${show.id}`} 
                      media={{
                        id: show.id,
                        name: show.name || "",
                        poster_path: show.poster_path,
                        backdrop_path: null,
                        overview: "",
                        first_air_date: show.first_air_date || "",
                        vote_average: 0,
                        vote_count: 0
                      }} 
                      type="tv" 
                    />
                  ))}
                </div>
              </section>
            )}
            
            {/* Load More Button */}
            {currentPage < searchResults.total_pages && (
              <div className="flex justify-center mt-8">
                <Button 
                  onClick={loadMore} 
                  disabled={isFetching}
                  className="bg-primary-red hover:bg-primary-red/80"
                >
                  {isFetching ? (
                    <>
                      <Loader className="mr-2 h-4 w-4 animate-spin" /> Loading...
                    </>
                  ) : (
                    "Load More"
                  )}
                </Button>
              </div>
            )}
          </div>
        )}
      </main>
      
      {/* Footer */}
      <footer className="py-6 border-t border-gray-800">
        <div className="container mx-auto px-4 text-center text-gray-400">
          <p>© 2023 Reelicious. All rights reserved.</p>
          <p className="text-sm mt-2">
            Powered by TMDB API. Not affiliated with any streaming service.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Search;
