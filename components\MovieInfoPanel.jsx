"use client";
import { Scrollbar } from "react-scrollbars-custom";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { getImageUrl } from "@/lib/image";
import {
  Users,
  Award,
  Clapperboard,
  Globe,
  TrendingUp,
  Bookmark,
  Heart,
  Share2
} from "lucide-react";
import { Button } from "./ui/button";
import SkeletonInfoPanel from "./skeletons/SkeletonInfoPanel";

function MovieInfoPanel({ movie }) {
  if (!movie) {
    return <SkeletonInfoPanel />;
  }

  // Format date for release date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get director from crew
  const getDirector = () => {
    if (!movie.credits?.crew) return "Unknown";
    const director = movie.credits.crew.find(person => person.job === "Director");
    return director ? director.name : "Unknown";
  };

  // Get writers from crew
  const getWriters = () => {
    if (!movie.credits?.crew) return [];
    return movie.credits.crew
      .filter(person => ["Screenplay", "Writer", "Story"].includes(person.job))
      .slice(0, 3);
  };

  return (
    <Scrollbar
      style={{ height: "100%" }}
      noDefaultStyles={false}
      trackYProps={{
        style: {
          width: "8px",
          background: "transparent",
          right: "2px"
        }
      }}
      thumbYProps={{
        style: {
          background: "rgba(107, 114, 128, 0.7)",
          borderRadius: "4px",
          width: "6px"
        }
      }}
    >
      <div className='p-6 space-y-6 bg-zinc-900/30 backdrop-blur-sm rounded-xl border border-gray-800/50 h-full'>
        <div className="flex items-center justify-between">
          <h2 className='text-xl font-bold'>Movie Details</h2>
          <div className="flex gap-2">
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Bookmark className="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Heart className="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Key Details */}
        <div className="space-y-4">
          {/* Status */}
          {movie.status && (
            <div className="flex items-center gap-2">
              <div className="bg-primary-red/20 p-2 rounded-full">
                <Clapperboard className="h-4 w-4 text-primary-red" />
              </div>
              <div>
                <p className="text-xs text-gray-400">Status</p>
                <p className="text-sm font-medium">{movie.status}</p>
              </div>
            </div>
          )}

          {/* Original Language */}
          {movie.original_language && (
            <div className="flex items-center gap-2">
              <div className="bg-blue-500/20 p-2 rounded-full">
                <Globe className="h-4 w-4 text-blue-500" />
              </div>
              <div>
                <p className="text-xs text-gray-400">Original Language</p>
                <p className="text-sm font-medium">
                  {movie.spoken_languages?.find(lang => lang.iso_639_1 === movie.original_language)?.english_name || movie.original_language}
                </p>
              </div>
            </div>
          )}

          {/* Popularity */}
          {movie.popularity && (
            <div className="flex items-center gap-2">
              <div className="bg-green-500/20 p-2 rounded-full">
                <TrendingUp className="h-4 w-4 text-green-500" />
              </div>
              <div>
                <p className="text-xs text-gray-400">Popularity</p>
                <p className="text-sm font-medium">{movie.popularity.toFixed(1)}</p>
              </div>
            </div>
          )}
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Crew Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Users className="h-5 w-5 text-primary-red" />
            <h3 className="text-lg font-semibold">Key Crew</h3>
          </div>

          {/* Director */}
          <div className="mb-4">
            <p className="text-xs text-gray-400 mb-2">Director</p>
            <div className="flex items-center gap-2">
              {movie.credits?.crew?.find(person => person.job === "Director")?.profile_path ? (
                <Avatar className="h-8 w-8 border border-gray-700/50">
                  <AvatarImage
                    src={getImageUrl(movie.credits.crew.find(person => person.job === "Director").profile_path, "w200")}
                    alt={getDirector()}
                  />
                  <AvatarFallback>{getDirector().charAt(0)}</AvatarFallback>
                </Avatar>
              ) : (
                <Avatar className="h-8 w-8 border border-gray-700/50">
                  <AvatarFallback>{getDirector().charAt(0)}</AvatarFallback>
                </Avatar>
              )}
              <span className="text-sm font-medium">{getDirector()}</span>
            </div>
          </div>

          {/* Writers */}
          {getWriters().length > 0 && (
            <div>
              <p className="text-xs text-gray-400 mb-2">Writing</p>
              <div className="space-y-2">
                {getWriters().map((writer, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {writer.profile_path ? (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarImage
                          src={getImageUrl(writer.profile_path, "w200")}
                          alt={writer.name}
                        />
                        <AvatarFallback>{writer.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    ) : (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarFallback>{writer.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <span className="text-sm font-medium">{writer.name}</span>
                      <p className="text-xs text-gray-400">{writer.job}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Additional Info */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Award className="h-5 w-5 text-primary-red" />
            <h3 className="text-lg font-semibold">Additional Info</h3>
          </div>

          <div className="space-y-3 text-sm">
            {movie.release_date && (
              <div className="flex justify-between">
                <span className="text-gray-400">Release Date</span>
                <span>{formatDate(movie.release_date)}</span>
              </div>
            )}

            {movie.vote_count > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-400">Vote Count</span>
                <span>{movie.vote_count.toLocaleString()}</span>
              </div>
            )}

            {movie.adult !== undefined && (
              <div className="flex justify-between">
                <span className="text-gray-400">Adult Content</span>
                <span>{movie.adult ? "Yes" : "No"}</span>
              </div>
            )}

            {movie.original_title && movie.original_title !== movie.title && (
              <div className="flex justify-between">
                <span className="text-gray-400">Original Title</span>
                <span className="text-right">{movie.original_title}</span>
              </div>
            )}

            {movie.imdb_id && (
              <div className="flex justify-between">
                <span className="text-gray-400">IMDB ID</span>
                <a
                  href={`https://www.imdb.com/title/${movie.imdb_id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary-red hover:underline"
                >
                  {movie.imdb_id}
                </a>
              </div>
            )}
          </div>
        </div>
      </div>
    </Scrollbar>
  );
}

export default MovieInfoPanel;
