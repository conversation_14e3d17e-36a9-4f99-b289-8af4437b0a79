import { create } from "zustand";
import { searchMulti } from "@/lib/actions";

const useSearchStore = create((set, get) => ({
  // Search data
  searchResults: null,
  searchLoading: false,
  searchPage: 1,
  searchError: null,
  searchFetching: false,
  searchHasMore: true,
  searchQuery: "",
  searchType: "all", // 'all', 'movie', 'tv'

  // Set search query
  setSearchQuery: (query) => {
    set({
      searchQuery: query,
      searchPage: 1,
      searchResults: null,
      searchHasMore: true,
    });
    if (query.trim()) {
      get().fetchSearchResults();
    }
  },

  // Set search type
  setSearchType: (type) => {
    set({
      searchType: type,
      searchPage: 1,
      searchResults: null,
      searchHasMore: true,
    });
    const { searchQuery } = get();
    if (searchQuery.trim()) {
      get().fetchSearchResults();
    }
  },

  // Fetch search results
  fetchSearchResults: async () => {
    const { searchPage, searchQuery, searchResults, searchType } = get();
    
    // If already fetching, don't fetch again
    if (get().searchFetching) return;
    
    // If no query, don't fetch
    if (!searchQuery.trim()) return;
    
    set({
      searchLoading: searchResults === null,
      searchFetching: true,
      searchError: null,
    });

    try {
      // Fetch search results
      const data = await searchMulti(searchQuery, searchPage);
      
      // Filter results by media type if needed
      let filteredResults = data.results;
      if (searchType !== 'all') {
        filteredResults = data.results.filter(item => item.media_type === searchType);
      }
      
      // Update state with new results
      set((state) => ({
        searchResults: searchPage === 1 
          ? { ...data, results: filteredResults } 
          : { 
              ...data, 
              results: [...(state.searchResults?.results || []), ...filteredResults] 
            },
        searchHasMore: data.page < data.total_pages,
        searchLoading: false,
        searchFetching: false,
      }));
    } catch (error) {
      set({
        searchError: error,
        searchLoading: false,
        searchFetching: false,
      });
    }
  },

  // Load more search results (for infinite scrolling)
  loadMoreResults: () => {
    const { searchHasMore, searchFetching } = get();
    if (!searchHasMore || searchFetching) return;
    
    set((state) => ({
      searchPage: state.searchPage + 1,
    }));
    
    get().fetchSearchResults();
  },

  // Reset search
  resetSearch: () => {
    set({
      searchResults: null,
      searchPage: 1,
      searchQuery: "",
      searchHasMore: true,
      searchType: "all",
    });
  },
}));

export default useSearchStore;
