"use client";

import { useEffect, useState } from "react";
import { motion } from "motion/react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "./ui/carousel";
import { getImageUrl } from "@/lib/image";
import Image from "next/image";
import Link from "next/link";
import { Star } from "lucide-react";
import { Play } from "lucide-react";
import { Calendar } from "lucide-react";
import SkeletonMediaGrid from "./skeletons/SkeletonMediaGrid";

function MediaGrid({ title, data, type, onLoadMore, hasMorePages, loading = false, error = null }) {
  if (loading && (!data || data.length === 0)) {
    return <SkeletonMediaGrid title={title} />;
  }

  if (error) {
    return (
      <div className="md:px-8 lg:px-12 xl:px-16 py-8">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">{title}</h2>
        <div className="p-6 bg-zinc-900/30 rounded-xl border border-red-500/20">
          <p className="text-red-500">Error loading content</p>
        </div>
      </div>
    );
  }
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Set visible after component mounts for animation
    setIsVisible(true);
  }, []);

  const formattedDate = (releaseDate) => {
    const fullYear = new Date(releaseDate).getFullYear();
    return fullYear;
  };

  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
  };

  const titleVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
      },
    },
  };

  return (
    <motion.section
      className="md:px-8 lg:px-12 xl:px-16"
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={containerVariants}
    >
      <div className="flex items-center justify-between mb-8">
        <motion.h2
          variants={titleVariants}
          initial="hidden"
          animate="visible"
          className="text-2xl md:text-3xl font-bold group flex items-center"
        >
          <span className="group-hover:text-primary-red transition-colors duration-300">
            {title}
          </span>
        </motion.h2>
        {type === "movie" ? (
          <motion.span
            className="text-xs font-medium px-4 py-2 bg-primary-red/20 text-primary-red rounded-full border border-primary-red/30 shadow-sm shadow-primary-red/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            Movies
          </motion.span>
        ) : (
          <motion.span
            className="text-xs font-medium px-4 py-2 bg-primary-purple/20 text-primary-purple rounded-full border border-primary-purple/30 shadow-sm shadow-primary-purple/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            TV Shows
          </motion.span>
        )}
      </div>

      <Carousel
        opts={{
          align: "start",
        }}
        className="w-full max-w-screen mx-auto"
      >
        <CarouselContent>
          {data.map((item) => (
            <CarouselItem key={item.id} className="basic-1/4 md:basis-1/7">
              <Link
                href={`/${type}/${item.id}`}
                className="block h-full overflow-hidden rounded-xl bg-card-dark border border-gray-800/50 hover:border-gray-700/80 transition-all duration-300 hover:shadow-xl hover:shadow-black/30 group"
              >
                <div className="relative aspect-[2/3] overflow-hidden rounded-t-xl">
                  <Image
                    src={getImageUrl(item.poster_path, "w780")}
                    alt={item.title}
                    className="h-full w-full object-cover transform transition-transform duration-500 group-hover:scale-110"
                    loading="lazy"
                    width={400}
                    height={600}
                  />
                  {/* Rating Badge */}
                  {item.vote_average && (
                    <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1 flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
                      <span className="text-xs font-medium">
                        {item.vote_average.toFixed(1)}
                      </span>
                    </div>
                  )}

                  {/* Play Button Overlay */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                    <motion.div
                      className="bg-primary-red rounded-full p-3 text-white"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <Play className="h-6 w-6 fill-white" />
                    </motion.div>
                  </div>

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="text-base font-semibold line-clamp-1 group-hover:text-primary-red transition-colors duration-300">
                    {item.title || item.original_name}
                  </h3>
                  <div className="flex items-center text-gray-400 mt-2 text-sm">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    <span>{formattedDate(item.release_date || item.first_air_date)}</span>

                    {/* Media Type Badge */}
                    <span
                      className={`ml-auto text-xs px-2 py-0.5 rounded-full ${
                        type === "movie"
                          ? "bg-primary-red/10 text-primary-red"
                          : "bg-primary-purple/10 text-primary-purple"
                      }`}
                    >
                      {type === "movie" ? "Movie" : "TV"}
                    </span>
                  </div>
                </div>
              </Link>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious />
        <CarouselNext />
      </Carousel>
    </motion.section>
  );
}

export default MediaGrid;
