import { useState, useEffect } from "react";
import { TMDBMovie, TMDBTVShow, TMDBPaginatedResponse } from "@/lib/api";
import MediaCard from "./MediaCard";
import { Button } from "@/components/ui/button";
import { Loader, ChevronRight } from "lucide-react";
import { motion } from "motion/react";

type MediaGridProps = {
  title: string;
  data?: TMDBPaginatedResponse<TMDBMovie | TMDBTVShow>;
  type: "movie" | "tv";
  loading: boolean;
  error: unknown;
  onLoadMore?: () => void;
  hasMorePages?: boolean;
};

const MediaGrid = ({
  title,
  data,
  type,
  loading,
  error,
  onLoadMore,
  hasMorePages,
}: MediaGridProps) => {
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    // Set visible after component mounts for animation
    setIsVisible(true);
  }, []);
  
  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  const titleVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: { 
      x: 0, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  // Show loading skeleton while data is being fetched
  if (loading && !data) {
    return (
      <section className="my-16 px-2">
        <div className="flex items-center justify-between mb-8">
          <div className="h-8 w-48 bg-gray-800 rounded-lg animate-pulse"></div>
          <div className="w-24 h-8 bg-gray-800 rounded-full animate-pulse"></div>
        </div>
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
          {[...Array(12)].map((_, index) => (
            <div 
              key={index} 
              className="bg-card-dark rounded-xl overflow-hidden animate-pulse shadow-lg transform transition-transform duration-300 hover:scale-105"
            >
              <div className="aspect-[2/3] bg-gray-800"></div>
              <div className="p-4">
                <div className="h-5 bg-gray-800 rounded mb-3"></div>
                <div className="h-4 bg-gray-800 rounded w-2/3"></div>
              </div>
            </div>
          ))}
        </div>
      </section>
    );
  }

  // Show error state
  if (error) {
    return (
      <motion.section 
        className="my-16 px-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.h2 
          className="text-2xl font-bold mb-6"
          variants={titleVariants}
          initial="hidden"
          animate="visible"
        >
          {title}
        </motion.h2>
        <div className="p-8 bg-red-900/20 border border-red-900 rounded-xl text-center backdrop-blur-sm">
          <p className="text-lg font-medium">Error loading data. Please try again later.</p>
          <p className="text-sm text-gray-400 mt-2">We're experiencing some technical difficulties.</p>
        </div>
      </motion.section>
    );
  }

  // Show empty state if no results
  if (!data?.results || data.results.length === 0) {
    return (
      <motion.section 
        className="my-16 px-2"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.h2 
          className="text-2xl font-bold mb-6"
          variants={titleVariants}
          initial="hidden"
          animate="visible"
        >
          {title}
        </motion.h2>
        <div className="p-8 bg-card-dark/50 rounded-xl text-center border border-gray-800 backdrop-blur-sm">
          <p className="text-lg font-medium text-gray-300">No results found.</p>
          <p className="text-sm text-gray-400 mt-2">Try adjusting your search or filters.</p>
        </div>
      </motion.section>
    );
  }

  return (
    <motion.section 
      className="my-16 px-2 overflow-hidden"
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={containerVariants}
    >
      <div className="flex items-center justify-between mb-8">
        <motion.h2 
          className="text-2xl md:text-3xl font-bold group flex items-center"
          variants={titleVariants}
        >
          <span className="group-hover:text-primary-red transition-colors duration-300">{title}</span>
          <ChevronRight className="h-5 w-5 ml-1 text-primary-red opacity-0 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-300" />
        </motion.h2>
        
        {type === "movie" ? (
          <motion.span 
            className="text-xs font-medium px-4 py-2 bg-primary-red/20 text-primary-red rounded-full border border-primary-red/30 shadow-sm shadow-primary-red/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            Movies
          </motion.span>
        ) : (
          <motion.span 
            className="text-xs font-medium px-4 py-2 bg-primary-purple/20 text-primary-purple rounded-full border border-primary-purple/30 shadow-sm shadow-primary-purple/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            TV Shows
          </motion.span>
        )}
      </div>
      
      <motion.div 
        className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6"
        variants={containerVariants}
      >
        {data.results.map((item, index) => (
          <motion.div 
            key={item.id} 
            variants={itemVariants}
            whileHover={{ 
              scale: 1.05,
              transition: { duration: 0.2 }
            }}
            className="will-change-transform"
          >
            <MediaCard 
              media={item} 
              type={type} 
            />
          </motion.div>
        ))}
      </motion.div>
      
      {/* Load More Button */}
      {onLoadMore && hasMorePages && (
        <motion.div 
          className="flex justify-center mt-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <Button 
            onClick={onLoadMore} 
            disabled={loading}
            className="bg-primary-red hover:bg-primary-red/90 px-10 py-6 h-auto rounded-full shadow-lg shadow-primary-red/20 transition-all duration-300 hover:shadow-primary-red/40 hover:scale-105 font-medium text-base"
            size="lg"
          >
            {loading ? (
              <>
                <Loader className="mr-2 h-5 w-5 animate-spin" /> Loading...
              </>
            ) : (
              "Load More"
            )}
          </Button>
        </motion.div>
      )}
    </motion.section>
  );
};

export default MediaGrid;
