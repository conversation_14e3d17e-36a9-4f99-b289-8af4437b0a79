"use client";

import { useEffect } from "react";
import { motion } from "motion/react";
import DiscoveryGrid from "@/components/DiscoveryGrid";
import FilterPanel from "@/components/FilterPanel";
import useDiscoverMovieStore from "@/store/discoverMovieStore";

function MoviesPage() {
  const {
    discoverMoviesData,
    discoverMoviesLoading,
    discoverMoviesError,
    discoverMoviesHasMore,
    fetchDiscoverMovies,
    loadMoreMovies,
    genresList,
    genresLoading,
    fetchGenres,
    filterParams,
    setFilterParams,
    resetFilters,
  } = useDiscoverMovieStore();

  useEffect(() => {
    // Fetch initial data if not already loaded
    if (!discoverMoviesData) {
      fetchDiscoverMovies();
    }

    // Fetch genres if not already loaded
    if (!genresList) {
      fetchGenres();
    }
  }, [discoverMoviesData, fetchDiscoverMovies, genresList, fetchGenres]);

  return (
    <main className="pt-20 pb-16">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <h1 className="text-3xl md:text-4xl font-bold mb-2">Discover Movies</h1>
          <p className="text-gray-400">
            Explore thousands of movies with advanced filtering options
          </p>
        </motion.div>

        {/* Filter Panel */}
        <FilterPanel
          type="movie"
          genres={genresList}
          filterParams={filterParams}
          onFilterChange={setFilterParams}
          onResetFilters={resetFilters}
        />

        {/* Movie Grid */}
        <DiscoveryGrid
          title="Movies"
          data={discoverMoviesData?.results || []}
          type="movie"
          loading={discoverMoviesLoading}
          error={discoverMoviesError}
          hasMore={discoverMoviesHasMore}
          onLoadMore={loadMoreMovies}
        />
      </div>
    </main>
  );
}

export default MoviesPage;