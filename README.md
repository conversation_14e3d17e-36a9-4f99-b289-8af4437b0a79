# RithFlix - Movie Streaming Platform

RithFlix is a modern movie and TV show streaming platform built with Next.js and optimized for deployment on Vercel.

## Features

- Browse trending and popular movies and TV shows
- Search for specific titles
- View detailed information about movies and TV shows
- Watch content with multiple server options
- Responsive design for all devices
- Dark mode support

## Tech Stack

- **Framework**: [Next.js 15](https://nextjs.org)
- **Styling**: [Tailwind CSS](https://tailwindcss.com)
- **UI Components**: [ShadCN](https://ui.shadcn.com)
- **State Management**: [Zustand](https://github.com/pmndrs/zustand)
- **Animations**: [Motion One](https://motion.dev)
- **Icons**: [Lucide React](https://lucide.dev)
- **API**: [TMDB API](https://www.themoviedb.org/documentation/api)

## Getting Started

### Prerequisites

- Node.js 18.17 or later
- pnpm (recommended)

### Development

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

3. Create a `.env` file with your TMDB API key:

```
API_KEY=your_tmdb_api_key
```

4. Start the development server:

```bash
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Building for Production

```bash
pnpm build
```

## Deployment on Vercel

This project is optimized for deployment on Vercel. To deploy:

1. Push your code to a Git repository (GitHub, GitLab, or Bitbucket)
2. Import your project to [Vercel](https://vercel.com/new)
3. Set the root directory to `next` if your Next.js app is in a subdirectory
4. Add your TMDB API key as an environment variable:
   - Name: `API_KEY`
   - Value: Your TMDB API key
5. Deploy!

For detailed deployment instructions, see [README.md.vercel](./README.md.vercel).

## Environment Variables

- `API_KEY`: Your TMDB API key (required)

## License

This project is licensed under the MIT License.
