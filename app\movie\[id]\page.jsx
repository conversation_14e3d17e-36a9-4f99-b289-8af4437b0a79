"use client";
import MovieInfoPanel from "@/components/MovieInfoPanel";
import MoviePlayer from "@/components/MoviePlayer";
import useMovieStore from "@/store/movieStore";
import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { motion } from "motion/react";
import { getImageUrl } from "@/lib/image";
import { Play, Star, Calendar, Clock, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import Image from "next/image";
import Link from "next/link";
import { Scrollbar } from "react-scrollbars-custom";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import SkeletonMovieDetails from "@/components/skeletons/SkeletonMovieDetails";
import AdsterraAd from "@/components/ads/ads728";

function MovieDetailsPage() {
  const { id } = useParams();
  const {
    movieDetailsData,
    movieDetailsLoading,
    movieDetailsError,
    fetchMovieDetails,
  } = useMovieStore();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Fetch movie details when the component mounts or when the ID changes
    if (!movieDetailsData || movieDetailsData.id !== id) {
      fetchMovieDetails(id);
      console.log("Fetching movie details for ID:", id);
    }

    // Set visible after component mounts for animation
    setIsVisible(true);
  }, [id, fetchMovieDetails]);

  if (movieDetailsLoading || !movieDetailsData) {
    return <SkeletonMovieDetails />;
  }

  if (movieDetailsError) {
    return (
      <div className='pt-20 pb-16 flex items-center justify-center min-h-screen'>
        <div className='text-center max-w-md mx-auto p-6 bg-zinc-900/50 rounded-lg border border-red-500/20'>
          <p className='text-red-500 text-lg mb-2'>
            Error loading movie details
          </p>
          <p className='text-gray-400'>
            {movieDetailsError.message || "Please try again later"}
          </p>
        </div>
      </div>
    );
  }

  // Format runtime to hours and minutes
  const formatRuntime = (minutes) => {
    if (!minutes) return "N/A";
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  // Format date to readable format
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: "numeric", month: "long", day: "numeric" };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <main className='pt-16 pb-16'>
      {/* Hero Section with Backdrop */}
      <div className='w-full h-full'>
        <AdsterraAd />
      </div>

      {/* Main Content */}
      <div className='container mx-auto px-4 mt-8'>
        {/* Fixed height container for main content */}
        <div className='flex flex-col lg:flex-row gap-8 lg:h-[800px]'>
          {/* Left Column - Movie and Details */}
          <div className='w-full lg:w-8/12 lg:pr-4'>
            <Scrollbar
              style={{ height: "100%" }}
              noDefaultStyles={false}
              trackYProps={{
                style: {
                  width: "8px",
                  background: "transparent",
                  right: "2px",
                },
              }}
              thumbYProps={{
                style: {
                  background: "rgba(107, 114, 128, 0.7)",
                  borderRadius: "4px",
                  width: "6px",
                },
              }}
            >
              {/* Movie Player Section */}
              <motion.div
                id='movie-player'
                className='mb-8 rounded-xl overflow-hidden shadow-2xl shadow-black/30 border border-gray-800/50'
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                <MoviePlayer />
              </motion.div>

              {/* Movie Details Section */}
              <motion.div
                className='bg-zinc-900/30 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 mb-8'
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <h2 className='text-2xl font-bold mb-4'>About the Movie</h2>
                <Separator className='mb-4 bg-gray-700/30' />
                <p className='text-gray-300 mb-6 leading-relaxed'>
                  {movieDetailsData.overview}
                </p>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                  {movieDetailsData.production_companies &&
                    movieDetailsData.production_companies.length > 0 && (
                      <div>
                        <h3 className='text-lg font-semibold mb-2'>
                          Production
                        </h3>
                        <div className='text-gray-400'>
                          {movieDetailsData.production_companies.map(
                            (company, index) => (
                              <span key={company.id}>
                                {company.name}
                                {index <
                                movieDetailsData.production_companies.length - 1
                                  ? ", "
                                  : ""}
                              </span>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {movieDetailsData.spoken_languages &&
                    movieDetailsData.spoken_languages.length > 0 && (
                      <div>
                        <h3 className='text-lg font-semibold mb-2'>
                          Languages
                        </h3>
                        <div className='text-gray-400'>
                          {movieDetailsData.spoken_languages.map(
                            (language, index) => (
                              <span key={language.iso_639_1}>
                                {language.english_name}
                                {index <
                                movieDetailsData.spoken_languages.length - 1
                                  ? ", "
                                  : ""}
                              </span>
                            )
                          )}
                        </div>
                      </div>
                    )}

                  {movieDetailsData.budget > 0 && (
                    <div>
                      <h3 className='text-lg font-semibold mb-2'>Budget</h3>
                      <div className='text-gray-400'>
                        ${movieDetailsData.budget.toLocaleString()}
                      </div>
                    </div>
                  )}

                  {movieDetailsData.revenue > 0 && (
                    <div>
                      <h3 className='text-lg font-semibold mb-2'>Revenue</h3>
                      <div className='text-gray-400'>
                        ${movieDetailsData.revenue.toLocaleString()}
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>

              {/* Cast Section */}
              {movieDetailsData.credits?.cast?.length > 0 && (
                <motion.div
                  className='bg-zinc-900/30 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 mb-8'
                  initial={{ opacity: 0, y: 20 }}
                  animate={{
                    opacity: isVisible ? 1 : 0,
                    y: isVisible ? 0 : 20,
                  }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <h2 className='text-2xl font-bold mb-4'>Cast</h2>
                  <Separator className='mb-6 bg-gray-700/30' />

                  <Carousel className='w-full'>
                    <CarouselContent>
                      {movieDetailsData.credits.cast
                        .slice(0, 20)
                        .map((person) => (
                          <CarouselItem
                            key={person.id}
                            className='basis-1/2 md:basis-1/3 lg:basis-1/5'
                          >
                            <div className='p-1'>
                              <div className='rounded-lg overflow-hidden bg-zinc-800 border border-gray-700/50 h-full'>
                                <div className='aspect-[2/3] relative'>
                                  <AspectRatio ratio={2 / 3}>
                                    {person.profile_path ? (
                                      <Image
                                        src={getImageUrl(
                                          person.profile_path,
                                          "w500"
                                        )}
                                        alt={person.name}
                                        fill
                                        className='object-cover'
                                      />
                                    ) : (
                                      <div className='w-full h-full flex items-center justify-center bg-zinc-800 text-zinc-500'>
                                        No Image
                                      </div>
                                    )}
                                  </AspectRatio>
                                </div>
                                <div className='p-3'>
                                  <h3 className='font-medium text-sm truncate'>
                                    {person.name}
                                  </h3>
                                  <p className='text-gray-400 text-xs truncate'>
                                    {person.character}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </CarouselItem>
                        ))}
                    </CarouselContent>
                    <CarouselPrevious className='left-2 bg-black/50 text-white hover:bg-black/70' />
                    <CarouselNext className='right-2 bg-black/50 text-white hover:bg-black/70' />
                  </Carousel>
                </motion.div>
              )}
            </Scrollbar>
          </div>

          {/* Right Column - Info Panel */}
          <div className='w-full lg:w-4/12 lg:h-full'>
            <motion.div
              className='h-full'
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              <MovieInfoPanel movie={movieDetailsData} />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Recommendations Section */}
      {movieDetailsData.recommendations?.results?.length > 0 && (
        <motion.div
          className='container mx-auto px-4 mt-12 mb-8'
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 20 }}
          transition={{ duration: 0.5, delay: 1 }}
        >
          <div className='flex items-center justify-between mb-6'>
            <h2 className='text-2xl font-bold'>Recommended Movies</h2>
            <Link href='/movie'>
              <Button
                variant='outline'
                size='sm'
                className='bg-black/30 backdrop-blur-sm border-gray-700/50 hover:bg-black/50'
              >
                View All Movies
              </Button>
            </Link>
          </div>

          <Carousel className='w-full'>
            <CarouselContent>
              {movieDetailsData.recommendations.results
                .slice(0, 12)
                .map((movie) => (
                  <CarouselItem
                    key={movie.id}
                    className='basis-1/2 md:basis-1/3 lg:basis-1/5 xl:basis-1/6'
                  >
                    <Link href={`/movie/${movie.id}`}>
                      <div className='p-1'>
                        <div className='rounded-lg overflow-hidden bg-zinc-800/50 border border-gray-700/30 hover:border-gray-600 transition-all duration-300 group h-full'>
                          <div className='aspect-[2/3] relative'>
                            <AspectRatio ratio={2 / 3}>
                              {movie.poster_path ? (
                                <Image
                                  src={getImageUrl(movie.poster_path, "w500")}
                                  alt={movie.title}
                                  fill
                                  className='object-cover transition-transform duration-500 group-hover:scale-110'
                                />
                              ) : (
                                <div className='w-full h-full flex items-center justify-center bg-zinc-800 text-zinc-500'>
                                  No Image
                                </div>
                              )}
                            </AspectRatio>
                            <div className='absolute inset-0 bg-gradient-to-t from-black/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300' />
                          </div>
                          <div className='p-3'>
                            <h3 className='font-medium text-sm truncate'>
                              {movie.title}
                            </h3>
                            <div className='flex items-center justify-between mt-1'>
                              <div className='flex items-center'>
                                <Star className='h-3 w-3 text-yellow-400 fill-yellow-400 mr-1' />
                                <span className='text-xs'>
                                  {movie.vote_average
                                    ? movie.vote_average.toFixed(1)
                                    : "N/A"}
                                </span>
                              </div>
                              {movie.release_date && (
                                <span className='text-xs text-gray-400'>
                                  {new Date(movie.release_date).getFullYear()}
                                </span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </CarouselItem>
                ))}
            </CarouselContent>
            <CarouselPrevious className='left-2 bg-black/50 text-white hover:bg-black/70' />
            <CarouselNext className='right-2 bg-black/50 text-white hover:bg-black/70' />
          </Carousel>
        </motion.div>
      )}
    </main>
  );
}

export default MovieDetailsPage;
