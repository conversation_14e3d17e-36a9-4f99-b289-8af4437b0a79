"use client";

import { useEffect, useRef, useState } from "react";
import { motion } from "motion/react";
import { getImageUrl } from "@/lib/image";
import Image from "next/image";
import Link from "next/link";
import { Star, Calendar } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

function DiscoveryGrid({ 
  title, 
  data, 
  type, 
  loading, 
  error, 
  hasMore, 
  onLoadMore 
}) {
  const [isVisible, setIsVisible] = useState(false);
  const observerRef = useRef(null);
  const loadingRef = useRef(null);

  useEffect(() => {
    // Set visible after component mounts for animation
    setIsVisible(true);
  }, []);

  useEffect(() => {
    // Set up intersection observer for infinite scrolling
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          onLoadMore();
        }
      },
      { threshold: 0.1 }
    );

    if (loadingRef.current) {
      observer.observe(loadingRef.current);
    }

    observerRef.current = observer;

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMore, loading, onLoadMore]);

  const formattedDate = (releaseDate) => {
    if (!releaseDate) return "Unknown";
    const fullYear = new Date(releaseDate).getFullYear();
    return fullYear;
  };

  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 15,
      },
    },
  };

  if (error) {
    return (
      <div className="flex justify-center items-center p-8 bg-zinc-900/50 rounded-xl">
        <p className="text-red-400">Error: {error.message || "Failed to load data"}</p>
      </div>
    );
  }

  return (
    <motion.section
      className="container mx-auto px-4 py-8"
      initial="hidden"
      animate={isVisible ? "visible" : "hidden"}
      variants={containerVariants}
    >
      <div className="flex items-center justify-between mb-8">
        <motion.h2
          variants={itemVariants}
          className="text-2xl md:text-3xl font-bold group flex items-center"
        >
          <span className="group-hover:text-primary-red transition-colors duration-300">
            {title}
          </span>
        </motion.h2>
        {type === "movie" ? (
          <motion.span
            className="text-xs font-medium px-4 py-2 bg-primary-red/20 text-primary-red rounded-full border border-primary-red/30 shadow-sm shadow-primary-red/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            Movies
          </motion.span>
        ) : (
          <motion.span
            className="text-xs font-medium px-4 py-2 bg-primary-purple/20 text-primary-purple rounded-full border border-primary-purple/30 shadow-sm shadow-primary-purple/10"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            TV Shows
          </motion.span>
        )}
      </div>

      {/* Grid Layout */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4 md:gap-6">
        {data && data.length > 0 ? (
          data.map((item) => (
            <motion.div key={item.id} variants={itemVariants}>
              <Link
                href={`/${type}/${item.id}`}
                className="block h-full overflow-hidden rounded-xl bg-card-dark border border-gray-800/50 hover:border-gray-700/80 transition-all duration-300 hover:shadow-xl hover:shadow-black/30 group"
              >
                <div className="relative aspect-[2/3] overflow-hidden rounded-t-xl">
                  <Image
                    src={getImageUrl(item.poster_path, "w500")}
                    alt={item.title || item.name}
                    className="h-full w-full object-cover transform transition-transform duration-500 group-hover:scale-110"
                    loading="lazy"
                    width={400}
                    height={600}
                  />
                  {/* Rating Badge */}
                  {item.vote_average && (
                    <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-sm rounded-full px-2 py-1 flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
                      <span className="text-xs font-medium">
                        {item.vote_average.toFixed(1)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-4">
                  <h3 className="text-base font-semibold line-clamp-1 group-hover:text-primary-red transition-colors duration-300">
                    {item.title || item.name}
                  </h3>
                  <div className="flex items-center text-gray-400 mt-2 text-sm">
                    <Calendar className="h-3.5 w-3.5 mr-1.5" />
                    <span>{formattedDate(item.release_date || item.first_air_date)}</span>

                    {/* Media Type Badge */}
                    <span
                      className={`ml-auto text-xs px-2 py-0.5 rounded-full ${
                        type === "movie"
                          ? "bg-primary-red/10 text-primary-red"
                          : "bg-primary-purple/10 text-primary-purple"
                      }`}
                    >
                      {type === "movie" ? "Movie" : "TV"}
                    </span>
                  </div>
                </div>
              </Link>
            </motion.div>
          ))
        ) : loading ? (
          // Loading skeletons
          Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="rounded-xl overflow-hidden bg-zinc-800/30">
              <Skeleton className="aspect-[2/3] w-full" />
              <div className="p-4">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-400">No results found</p>
          </div>
        )}
      </div>

      {/* Loading indicator for infinite scroll */}
      {hasMore && (
        <div 
          ref={loadingRef} 
          className="w-full flex justify-center py-8"
        >
          {loading && (
            <div className="flex flex-col items-center">
              <div className="w-10 h-10 border-4 border-primary-red/30 border-t-primary-red rounded-full animate-spin"></div>
              <p className="mt-4 text-sm text-gray-400">Loading more...</p>
            </div>
          )}
        </div>
      )}
    </motion.section>
  );
}

export default DiscoveryGrid;
