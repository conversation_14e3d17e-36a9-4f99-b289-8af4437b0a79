
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import Header from "@/components/Header";
import MediaGrid from "@/components/MediaGrid";

const Movies = () => {
  // State for pagination
  const [popularMoviesPage, setPopularMoviesPage] = useState(1);

  // Fetch popular movies
  const {
    data: popularMoviesData,
    isLoading: popularMoviesLoading,
    error: popularMoviesError,
    isFetching: popularMoviesFetching
  } = useQuery({
    queryKey: ["popularMovies", popularMoviesPage],
    queryFn: () => api.getPopularMovies(popularMoviesPage),
    placeholderData: previousData => previousData,
  });

  // Load more function
  const loadMorePopularMovies = () => {
    if (popularMoviesData && popularMoviesPage < popularMoviesData.total_pages) {
      setPopularMoviesPage(prev => prev + 1);
    }
  };

  return (
    <div className="min-h-screen bg-background-dark">
      <Header />
      
      <main className="container mx-auto px-4 pt-24 pb-12">
        <h1 className="text-4xl font-bold mb-6">Movies</h1>
        <p className="text-gray-400 mb-8">
          Explore our collection of movies
        </p>
        
        {/* Popular Movies Section */}
        <MediaGrid
          title="Popular Movies"
          data={popularMoviesData}
          type="movie"
          loading={popularMoviesLoading || popularMoviesFetching}
          error={popularMoviesError}
          onLoadMore={loadMorePopularMovies}
          hasMorePages={
            popularMoviesData ? 
            popularMoviesPage < popularMoviesData.total_pages : 
            false
          }
        />
      </main>
      
      {/* Footer */}
      <footer className="py-6 border-t border-gray-800">
        <div className="container mx-auto px-4 text-center text-gray-400">
          <p>© 2023 Reelicious. All rights reserved.</p>
          <p className="text-sm mt-2">
            Powered by TMDB API. Not affiliated with any streaming service.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Movies;
