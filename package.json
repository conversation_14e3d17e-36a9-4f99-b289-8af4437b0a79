{"name": "reactflix", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build", "analyze": "ANALYZE=true next build"}, "dependencies": {"@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "embla-carousel-react": "^8.6.0", "lucide-react": "^0.510.0", "motion": "^12.11.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-iframe": "^1.8.5", "react-scrollbars-custom": "^4.1.1", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwind-merge": "^3.3.0", "tailwindcss": "^4"}}