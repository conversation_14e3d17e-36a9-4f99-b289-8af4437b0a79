"use client";

import { Suspense } from "react";
import { motion } from "motion/react";
import { Search as SearchIcon } from "lucide-react";
import SkeletonSearchResults from "@/components/skeletons/SkeletonSearchResults";
import SearchContent from "./SearchContent";
import SearchResults from "./SearchResults";

function SearchPage() {
  return (
    <main className="pt-24 min-h-screen bg-background-dark">
      {/* Search Header */}
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="bg-card-dark border border-gray-800 rounded-xl p-6 mb-8 shadow-xl"
        >
          <h1 className="text-2xl md:text-3xl font-bold mb-6 flex items-center">
            <SearchIcon className="mr-3 text-primary-red" />
            Search
          </h1>

          {/* Wrap the component that uses useSearchParams in Suspense */}
          <Suspense fallback={<div className="py-4">Loading search...</div>}>
            <SearchContent />
          </Suspense>
        </motion.div>
      </div>

      {/* Search Results Section */}
      <Suspense fallback={<SkeletonSearchResults />}>
        <SearchResults />
      </Suspense>
    </main>
  );
}

export default SearchPage;
