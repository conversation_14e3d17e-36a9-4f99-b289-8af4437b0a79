import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { api, getImageUrl, TMDBEpisode } from "@/lib/api";
import Header from "@/components/Header";
import VideoPlayer from "@/components/VideoPlayer";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Star, Calendar, ArrowLeft, Loader, Play } from "lucide-react";

const TVShowDetail = () => {
  const { id } = useParams();
  const tvId = id ? parseInt(id) : 0;

  // State for selected season and episode
  const [selectedSeason, setSelectedSeason] = useState(1);
  const [selectedEpisode, setSelectedEpisode] = useState(1);
  const [episodes, setEpisodes] = useState<TMDBEpisode[]>([]);

  // Fetch TV show details
  const {
    data: tvShow,
    isLoading: tvShowLoading,
    error: tvShowError,
  } = useQuery({
    queryKey: ["tvShow", tvId],
    queryFn: async () => {
      const [tvShowDetails, titleImage] = await Promise.all([
        api.getTVShowDetails(tvId),
        api.getTVShowTitleImage(tvId),
      ]);
      return { ...tvShowDetails, titleImage };
    },
    enabled: !!tvId,
  });

  const titleLogos = tvShow?.titleImage.logos;
  const titlelogo = titleLogos?.find((logo) => logo.iso_639_1 === "en");
  // console.log(titlelogo);

  // Fetch season details when season changes
  const {
    data: seasonData,
    isLoading: seasonLoading,
    error: seasonError,
  } = useQuery({
    queryKey: ["tvSeason", tvId, selectedSeason],
    queryFn: () => api.getTVSeasonDetails(tvId, selectedSeason),
    enabled: !!tvId && !!selectedSeason && !!tvShow,
  });

  // Update episodes list when season data changes
  useEffect(() => {
    if (seasonData && seasonData.episodes) {
      setEpisodes(seasonData.episodes);
      // Reset selected episode to 1 when changing seasons
      setSelectedEpisode(1);
    }
  }, [seasonData]);

  // Loading state
  if (tvShowLoading) {
    return (
      <div className='min-h-screen bg-background-dark flex items-center justify-center'>
        <Loader className='h-12 w-12 text-primary-red animate-spin' />
      </div>
    );
  }

  // Error state
  if (tvShowError || !tvShow) {
    return (
      <div className='min-h-screen bg-background-dark'>
        <Header />
        <div className='container mx-auto px-4 pt-24 pb-12'>
          <div className='flex items-center mb-6'>
            <Link to='/'>
              <Button variant='ghost' size='sm'>
                <ArrowLeft className='h-4 w-4 mr-2' /> Back
              </Button>
            </Link>
          </div>
          <div className='p-8 bg-card-dark rounded-lg text-center'>
            <h2 className='text-2xl font-bold mb-4'>Error</h2>
            <p>Unable to load TV show details. Please try again later.</p>
          </div>
        </div>
      </div>
    );
  }

  // Format air date
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Unknown";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className='min-h-screen bg-background-dark'>
      <Header />

      {/* Backdrop image - Increased height from 50vh to 70vh and reduced blur */}
      <div
        className='absolute top-0 left-0 right-0 h-[70vh] bg-cover bg-center bg-no-repeat opacity-40'
        style={{
          backgroundImage: `url(${getImageUrl(
            tvShow.backdrop_path,
            "w780"
          )})`,
        }}
      />

      <main className='relative container mx-auto px-4 pt-20 pb-12'>
        <div className='flex items-center mb-6'>
          <Link to='/'>
            <Button variant='ghost' size='sm'>
              <ArrowLeft className='h-4 w-4 mr-2 text-white' />
              <span className='text-white'>Back</span>
            </Button>
          </Link>
        </div>

        <div className='flex flex-col md:flex-row gap-8 items-start'>
          {/* Poster */}
          <div className='w-full md:w-1/3 lg:w-1/4 flex-shrink-0 animate-fade-in'>
            <img
              src={getImageUrl(tvShow.poster_path, "w500")}
              alt={tvShow.name}
              className='w-full rounded-lg shadow-lg object-cover'
            />
          </div>

          {/* TV show details */}
          <div className='flex-grow animate-slide-up'>
            {/* <h1 className="text-3xl md:text-4xl font-bold mb-3">{tvShow.name}</h1> */}
            <img
              src={getImageUrl(titlelogo.file_path, "w500")}
              alt={tvShow.title}
              className='rounded-lg shadow-lg object-cover mb-6'
            />

            {/* TV show metadata */}
            <div className='flex flex-wrap items-center gap-x-6 gap-y-3 mb-6 text-gray-300'>
              {tvShow.vote_average ? (
                <div className='flex items-center'>
                  <Star className='h-5 w-5 text-yellow-400 fill-yellow-400 mr-1.5' />
                  <span className="font-medium">{tvShow.vote_average.toFixed(1)}</span><span className="text-sm text-gray-400">/10</span>
                </div>
              ) : null}

              {tvShow.first_air_date ? (
                <div className='flex items-center'>
                  <Calendar className='h-5 w-5 text-gray-400 mr-1.5' />
                  <span>{formatDate(tvShow.first_air_date)}</span>
                </div>
              ) : null}

              {tvShow.number_of_seasons ? (
                <div className='flex items-center'>
                  {/* Consider adding an icon for seasons if available */}
                  <span>
                    {tvShow.number_of_seasons}{" "}
                    {tvShow.number_of_seasons === 1 ? "Season" : "Seasons"}
                  </span>
                </div>
              ) : null}
            </div>

            {/* Genres */}
            {tvShow.genres && tvShow.genres.length > 0 && (
              <div className='flex flex-wrap gap-2 mb-6'>
                {tvShow.genres.map((genre) => (
                  <span
                    key={genre.id}
                    className='px-3 py-1.5 bg-gray-700/50 hover:bg-gray-600/70 rounded-full text-xs font-medium text-gray-300 transition-colors duration-150'
                  >
                    {genre.name}
                  </span>
                ))}
              </div>
            )}

            {/* Overview */}
            <div className='mb-8'>
              <h2 className='text-2xl font-semibold mb-3 text-white'>Overview</h2>
              <p className='text-gray-300 leading-relaxed text-base'>
                {tvShow.overview || "No overview available."}
              </p>
            </div>
          </div>
        </div>

        {/* Season and Episode Selection - Moved to next line */}
        <div className='mt-8'>
          <h2 className='text-xl font-semibold mb-4'>Watch Now</h2>
          <div className='flex flex-wrap gap-4 mb-6'>
            {/* Season selector */}
            <div>
              <label className='block text-sm text-gray-400 mb-1'>Season</label>
              <Select
                value={String(selectedSeason)}
                onValueChange={(value) => setSelectedSeason(parseInt(value))}
              >
                <SelectTrigger className='w-[140px] text-black'>
                  <SelectValue placeholder='Select season' />
                </SelectTrigger>
                <SelectContent>
                  {tvShow.seasons
                    ?.filter((season) => season.season_number > 0) // Filter out specials (season 0)
                    .map((season) => (
                      <SelectItem
                        key={season.id}
                        value={String(season.season_number)}
                        className="text-black"
                      >
                        Season {season.season_number}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            {/* Episode selector */}
            <div>
              <label className='block text-sm text-gray-400 mb-1'>
                Episode
              </label>
              <Select
                value={String(selectedEpisode)}
                onValueChange={(value) => setSelectedEpisode(parseInt(value))}
                disabled={seasonLoading || episodes.length === 0}
              >
                <SelectTrigger className='w-[180px] text-black'>
                  <SelectValue
                    placeholder={
                      seasonLoading ? "Loading..." : "Select episode"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {episodes.map((episode) => (
                    <SelectItem
                      key={episode.id}
                      value={String(episode.episode_number)}
                    >
                      {episode.episode_number}. {episode.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Video player */}
          <VideoPlayer
            tmdbId={tvId}
            type='tv'
            season={selectedSeason}
            episode={selectedEpisode}
          />
        </div>

        {/* Episodes list */}
        <div className='mt-8'>
          <h2 className='text-xl font-semibold mb-4'>Episodes</h2>
          {seasonLoading ? (
            <div className='flex items-center justify-center p-8'>
              <Loader className='h-8 w-8 text-primary-red animate-spin' />
            </div>
          ) : seasonError ? (
            <div className='p-4 border border-red-800 bg-red-900/20 rounded-lg'>
              <p>Failed to load episodes. Please try again.</p>
            </div>
          ) : episodes.length === 0 ? (
            <div className='p-4 bg-card-dark rounded-lg'>
              <p>No episodes found for this season.</p>
            </div>
          ) : (
            <div className='grid gap-4 animate-fade-in'>
              {episodes.map((episode) => (
                <div
                  key={episode.id}
                  className={`p-4 bg-card-dark rounded-lg transition-colors ${
                    episode.episode_number === selectedEpisode
                      ? "border border-primary-red"
                      : "hover:bg-gray-800"
                  }`}
                >
                  <div className='flex gap-4'>
                    {/* Episode still image */}
                    <div className='w-32 h-20 bg-black rounded overflow-hidden flex-shrink-0'>
                      {episode.still_path ? (
                        <img
                          src={getImageUrl(episode.still_path, "w300")}
                          alt={episode.name}
                          className='w-full h-full object-cover'
                        />
                      ) : (
                        <div className='w-full h-full flex items-center justify-center bg-gray-900'>
                          <span className='text-xs text-gray-500'>
                            No image
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Episode details */}
                    <div className='flex-grow'>
                      <div className='flex justify-between items-start'>
                        <h3 className='font-medium'>
                          {episode.episode_number}. {episode.name}
                        </h3>
                        <Button
                          variant='ghost'
                          size='sm'
                          className='text-primary-red'
                          onClick={() =>
                            setSelectedEpisode(episode.episode_number)
                          }
                        >
                          <Play className='h-4 w-4 mr-1' />
                          <span>Play</span>
                        </Button>
                      </div>
                      <p className='text-sm text-gray-400 mt-1'>
                        {formatDate(episode.air_date)}
                      </p>
                      <p className='text-sm text-gray-300 mt-2 line-clamp-2'>
                        {episode.overview || "No description available."}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className='py-6 border-t border-gray-800'>
        <div className='container mx-auto px-4 text-center text-gray-400'>
          <p>© 2023 Reelicious. All rights reserved.</p>
          <p className='text-sm mt-2'>
            Powered by TMDB API. Not affiliated with any streaming service.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default TVShowDetail;
