"use client";
import { Scrollbar } from "react-scrollbars-custom";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { getImageUrl } from "@/lib/image";
import {
  Users,
  Award,
  Tv,
  Globe,
  TrendingUp,
  Bookmark,
  Heart,
  Share2,
  Calendar,
  Star
} from "lucide-react";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import SkeletonInfoPanel from "./skeletons/SkeletonInfoPanel";

function TVShowInfoPanel({ tvShow }) {
  if (!tvShow) {
    return <SkeletonInfoPanel />;
  }

  // Format date for first air date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const options = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Get creators
  const getCreators = () => {
    if (!tvShow.created_by || tvShow.created_by.length === 0) return [];
    return tvShow.created_by.slice(0, 3);
  };

  // Get executive producers from crew
  const getExecutiveProducers = () => {
    if (!tvShow.credits?.crew) return [];
    return tvShow.credits.crew
      .filter(person => person.job === "Executive Producer")
      .slice(0, 3);
  };

  return (
    <Scrollbar
      style={{ height: "100%" }}
      noDefaultStyles={false}
      trackYProps={{
        style: {
          width: "8px",
          background: "transparent",
          right: "2px"
        }
      }}
      thumbYProps={{
        style: {
          background: "rgba(107, 114, 128, 0.7)",
          borderRadius: "4px",
          width: "6px"
        }
      }}
    >
      <div className='p-6 space-y-6 bg-zinc-900/30 backdrop-blur-sm rounded-xl border border-gray-800/50 h-full'>
        <div className="flex items-center justify-between">
          <h2 className='text-xl font-bold'>TV Show Details</h2>
          <div className="flex gap-2">
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Bookmark className="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Heart className="h-4 w-4" />
            </Button>
            <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full">
              <Share2 className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Key Details */}
        <div>
          <div className="space-y-4">
            {/* Status */}
            {tvShow.status && (
              <div className="flex items-center gap-2">
                <div className="bg-primary-red/20 p-2 rounded-full">
                  <Tv className="h-4 w-4 text-primary-red" />
                </div>
                <div>
                  <p className="text-xs text-gray-400">Status</p>
                  <p className="text-sm font-medium">{tvShow.status}</p>
                </div>
              </div>
            )}

            {/* Network */}
            {tvShow.networks && tvShow.networks.length > 0 && (
              <div className="flex items-center gap-2">
                <div className="bg-blue-500/20 p-2 rounded-full">
                  <Globe className="h-4 w-4 text-blue-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-400">Network</p>
                  <p className="text-sm font-medium">
                    {tvShow.networks.map(network => network.name).join(", ")}
                  </p>
                </div>
              </div>
            )}

            {/* Popularity */}
            {tvShow.popularity && (
              <div className="flex items-center gap-2">
                <div className="bg-green-500/20 p-2 rounded-full">
                  <TrendingUp className="h-4 w-4 text-green-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-400">Popularity</p>
                  <p className="text-sm font-medium">{tvShow.popularity.toFixed(1)}</p>
                </div>
              </div>
            )}

            {/* Type */}
            {tvShow.type && (
              <div className="flex items-center gap-2">
                <div className="bg-purple-500/20 p-2 rounded-full">
                  <Tv className="h-4 w-4 text-purple-500" />
                </div>
                <div>
                  <p className="text-xs text-gray-400">Type</p>
                  <p className="text-sm font-medium">{tvShow.type}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Creators Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Users className="h-5 w-5 text-primary-red" />
            <h3 className="text-lg font-semibold">Key People</h3>
          </div>

          {/* Creators */}
          {getCreators().length > 0 && (
            <div className="mb-4">
              <p className="text-xs text-gray-400 mb-2">Created By</p>
              <div className="space-y-2">
                {getCreators().map((creator, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {creator.profile_path ? (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarImage
                          src={getImageUrl(creator.profile_path, "w200")}
                          alt={creator.name}
                        />
                        <AvatarFallback>{creator.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    ) : (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarFallback>{creator.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    )}
                    <span className="text-sm font-medium">{creator.name}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Executive Producers */}
          {getExecutiveProducers().length > 0 && (
            <div>
              <p className="text-xs text-gray-400 mb-2">Executive Producers</p>
              <div className="space-y-2">
                {getExecutiveProducers().map((producer, index) => (
                  <div key={index} className="flex items-center gap-2">
                    {producer.profile_path ? (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarImage
                          src={getImageUrl(producer.profile_path, "w200")}
                          alt={producer.name}
                        />
                        <AvatarFallback>{producer.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    ) : (
                      <Avatar className="h-8 w-8 border border-gray-700/50">
                        <AvatarFallback>{producer.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                    )}
                    <div>
                      <span className="text-sm font-medium">{producer.name}</span>
                      <p className="text-xs text-gray-400">{producer.job}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <Separator className="bg-gray-700/30" />

        {/* Additional Info */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Award className="h-5 w-5 text-primary-red" />
            <h3 className="text-lg font-semibold">Additional Info</h3>
          </div>

          <div className="space-y-3 text-sm">
            {tvShow.first_air_date && (
              <div className="flex justify-between">
                <span className="text-gray-400">First Air Date</span>
                <span>{formatDate(tvShow.first_air_date)}</span>
              </div>
            )}

            {tvShow.last_air_date && (
              <div className="flex justify-between">
                <span className="text-gray-400">Last Air Date</span>
                <span>{formatDate(tvShow.last_air_date)}</span>
              </div>
            )}

            {tvShow.number_of_seasons > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-400">Seasons</span>
                <span>{tvShow.number_of_seasons}</span>
              </div>
            )}

            {tvShow.number_of_episodes > 0 && (
              <div className="flex justify-between">
                <span className="text-gray-400">Episodes</span>
                <span>{tvShow.number_of_episodes}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </Scrollbar>
  );
}

export default TVShowInfoPanel;
