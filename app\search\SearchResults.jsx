"use client";

import { useSearchParams } from "next/navigation";
import DiscoveryGrid from "@/components/DiscoveryGrid";
import useSearchStore from "@/store/searchStore";
import SkeletonSearchResults from "@/components/skeletons/SkeletonSearchResults";

export default function SearchResults() {
  const searchParams = useSearchParams();
  const query = searchParams.get("q") || "";

  const {
    searchResults,
    searchLoading,
    searchError,
    searchHasMore,
    searchQuery,
    searchType,
    loadMoreResults,
  } = useSearchStore();

  // Format results for DiscoveryGrid
  const formatResults = () => {
    if (!searchResults?.results) return [];

    return searchResults.results.map(item => {
      // For multi search, we need to normalize the data structure
      if (item.media_type === "movie") {
        return {
          ...item,
          title: item.title,
          release_date: item.release_date,
        };
      } else if (item.media_type === "tv") {
        return {
          ...item,
          name: item.name,
          first_air_date: item.first_air_date,
        };
      }
      return item;
    });
  };

  if (!searchQuery) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <p className="text-gray-400">Enter a search term to find movies and TV shows</p>
      </div>
    );
  }

  if (searchLoading && (!searchResults || !searchResults.results || searchResults.results.length === 0)) {
    return <SkeletonSearchResults />;
  }

  return (
    <DiscoveryGrid
      title={`Results for "${searchQuery}"`}
      data={formatResults()}
      type={searchType === "tv" ? "tv" : "movie"}
      loading={searchLoading}
      error={searchError}
      hasMore={searchHasMore}
      onLoadMore={loadMoreResults}
    />
  );
}
