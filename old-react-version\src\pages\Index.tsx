
import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import Header from "@/components/Header";
import MediaGrid from "@/components/MediaGrid";
import FeaturedCarousel from "@/components/FeaturedCarousel";
import { motion } from "motion/react";
import { ArrowRight, Flame, TrendingUp, Award, Star } from "lucide-react";

const Index = () => {
  // State for pagination
  const [trendingMoviesPage, setTrendingMoviesPage] = useState(1);
  const [trendingTVPage, setTrendingTVPage] = useState(1);
  const [popularMoviesPage, setPopularMoviesPage] = useState(1);
  const [popularTVPage, setPopularTVPage] = useState(1);
  const [isVisible, setIsVisible] = useState(false);
  
  useEffect(() => {
    // Set visible after component mounts for animation
    setIsVisible(true);
  }, []);

  // Fetch trending movies
  const {
    data: trendingMoviesData,
    isLoading: trendingMoviesLoading,
    error: trendingMoviesError,
    isFetching: trendingMoviesFetching
  } = useQuery({
    queryKey: ["trendingMovies", trendingMoviesPage],
    queryFn: () => api.getTrendingMovies(trendingMoviesPage),
    placeholderData: previousData => previousData,
  });

  // Fetch trending TV shows
  const {
    data: trendingTVData,
    isLoading: trendingTVLoading,
    error: trendingTVError,
    isFetching: trendingTVFetching
  } = useQuery({
    queryKey: ["trendingTV", trendingTVPage],
    queryFn: () => api.getTrendingTVShows(trendingTVPage),
    placeholderData: previousData => previousData,
  });

  // Fetch popular movies
  const {
    data: popularMoviesData,
    isLoading: popularMoviesLoading,
    error: popularMoviesError,
    isFetching: popularMoviesFetching
  } = useQuery({
    queryKey: ["popularMovies", popularMoviesPage],
    queryFn: () => api.getPopularMovies(popularMoviesPage),
    placeholderData: previousData => previousData,
  });

  // Fetch popular TV shows
  const {
    data: popularTVData,
    isLoading: popularTVLoading,
    error: popularTVError,
    isFetching: popularTVFetching
  } = useQuery({
    queryKey: ["popularTV", popularTVPage],
    queryFn: () => api.getPopularTVShows(popularTVPage),
    placeholderData: previousData => previousData,
  });

  // Load more functions
  const loadMoreTrendingMovies = () => {
    if (trendingMoviesData && trendingMoviesPage < trendingMoviesData.total_pages) {
      setTrendingMoviesPage(prev => prev + 1);
    }
  };

  const loadMoreTrendingTV = () => {
    if (trendingTVData && trendingTVPage < trendingTVData.total_pages) {
      setTrendingTVPage(prev => prev + 1);
    }
  };

  const loadMorePopularMovies = () => {
    if (popularMoviesData && popularMoviesPage < popularMoviesData.total_pages) {
      setPopularMoviesPage(prev => prev + 1);
    }
  };

  const loadMorePopularTV = () => {
    if (popularTVData && popularTVPage < popularTVData.total_pages) {
      setPopularTVPage(prev => prev + 1);
    }
  };
  
  // Animation variants
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-background-dark">
      <Header />
      
      <main className="pt-20 pb-16">
        {/* Hero Section with Featured Carousel */}
        {!trendingMoviesLoading && trendingMoviesData?.results && (
          <motion.div 
            className="mb-16 md:px-8 lg:px-12 xl:px-16 md:block hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8 }}
          >
            <FeaturedCarousel 
              items={trendingMoviesData.results} 
              type="movie" 
            />
          </motion.div>
        )}
        
        <div className="container mx-auto px-4 md:px-6">
          {/* Mobile Welcome Section */}
          <motion.div 
            className="md:hidden block mb-12"
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <h1 className="text-4xl font-bold mb-4 bg-gradient-to-r from-primary-red to-primary-purple bg-clip-text text-transparent">
              Welcome to RithFlix
            </h1>
            <p className="text-gray-300 text-lg mb-8 max-w-xl">
              Stream the latest movies and TV shows all in one place. Discover new favorites and classics.
            </p>
            
            {/* Quick Category Links for Mobile */}
            <div className="grid grid-cols-2 gap-4 mt-6">
              <div className="bg-gradient-to-br from-primary-red/20 to-primary-red/5 p-4 rounded-xl border border-primary-red/20 flex items-center gap-3">
                <Flame className="h-6 w-6 text-primary-red" />
                <span className="font-medium">Trending Now</span>
              </div>
              <div className="bg-gradient-to-br from-primary-purple/20 to-primary-purple/5 p-4 rounded-xl border border-primary-purple/20 flex items-center gap-3">
                <Star className="h-6 w-6 text-primary-purple" />
                <span className="font-medium">Top Rated</span>
              </div>
            </div>
          </motion.div>
          
          {/* Category Highlights Section */}
          <motion.div 
            className="mb-16 hidden md:block"
            initial="hidden"
            animate={isVisible ? "visible" : "hidden"}
            variants={fadeInUp}
          >
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-3xl font-bold">Discover</h2>
              <div className="flex items-center text-primary-red hover:text-white transition-colors duration-300 cursor-pointer group">
                <span className="text-sm font-medium">Browse All</span>
                <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-gradient-to-br from-primary-red/20 to-primary-red/5 p-6 rounded-xl border border-primary-red/20 hover:border-primary-red/40 transition-all duration-300 hover:shadow-lg hover:shadow-primary-red/5 group">
                <TrendingUp className="h-8 w-8 text-primary-red mb-4 group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-xl font-bold mb-2">Trending</h3>
                <p className="text-gray-400">What everyone's watching right now</p>
              </div>
              
              <div className="bg-gradient-to-br from-primary-purple/20 to-primary-purple/5 p-6 rounded-xl border border-primary-purple/20 hover:border-primary-purple/40 transition-all duration-300 hover:shadow-lg hover:shadow-primary-purple/5 group">
                <Star className="h-8 w-8 text-primary-purple mb-4 group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-xl font-bold mb-2">Top Rated</h3>
                <p className="text-gray-400">Highest rated by critics and viewers</p>
              </div>
              
              <div className="bg-gradient-to-br from-blue-500/20 to-blue-500/5 p-6 rounded-xl border border-blue-500/20 hover:border-blue-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/5 group">
                <Flame className="h-8 w-8 text-blue-500 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-xl font-bold mb-2">New Releases</h3>
                <p className="text-gray-400">Fresh content added this week</p>
              </div>
              
              <div className="bg-gradient-to-br from-amber-500/20 to-amber-500/5 p-6 rounded-xl border border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 hover:shadow-lg hover:shadow-amber-500/5 group">
                <Award className="h-8 w-8 text-amber-500 mb-4 group-hover:scale-110 transition-transform duration-300" />
                <h3 className="text-xl font-bold mb-2">Award Winners</h3>
                <p className="text-gray-400">Acclaimed movies and shows</p>
              </div>
            </div>
          </motion.div>
          
          {/* Content Sections */}
          <div className="space-y-8">
            {/* Trending Movies Section */}
            <MediaGrid
              title="Trending Movies"
              data={trendingMoviesData}
              type="movie"
              loading={trendingMoviesLoading || trendingMoviesFetching}
              error={trendingMoviesError}
              onLoadMore={loadMoreTrendingMovies}
              hasMorePages={
                trendingMoviesData ? 
                trendingMoviesPage < trendingMoviesData.total_pages : 
                false
              }
            />
            
            {/* Trending TV Shows Section */}
            <MediaGrid
              title="Trending TV Shows"
              data={trendingTVData}
              type="tv"
              loading={trendingTVLoading || trendingTVFetching}
              error={trendingTVError}
              onLoadMore={loadMoreTrendingTV}
              hasMorePages={
                trendingTVData ? 
                trendingTVPage < trendingTVData.total_pages : 
                false
              }
            />
            
            {/* Popular Movies Section */}
            <MediaGrid
              title="Popular Movies"
              data={popularMoviesData}
              type="movie"
              loading={popularMoviesLoading || popularMoviesFetching}
              error={popularMoviesError}
              onLoadMore={loadMorePopularMovies}
              hasMorePages={
                popularMoviesData ? 
                popularMoviesPage < popularMoviesData.total_pages : 
                false
              }
            />
            
            {/* Popular TV Shows Section */}
            <MediaGrid
              title="Popular TV Shows"
              data={popularTVData}
              type="tv"
              loading={popularTVLoading || popularTVFetching}
              error={popularTVError}
              onLoadMore={loadMorePopularTV}
              hasMorePages={
                popularTVData ? 
                popularTVPage < popularTVData.total_pages : 
                false
              }
            />
          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="py-10 border-t border-gray-800/50 bg-background-dark/90 backdrop-blur-sm">
        <div className="container mx-auto px-4 md:px-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-6 md:mb-0">
              <h2 className="text-2xl font-bold text-primary-red mb-2">RithFlix</h2>
              <p className="text-gray-400 max-w-md">
                Your ultimate destination for movies and TV shows. Discover, explore, and enjoy.
              </p>
            </div>
            
            <div className="flex flex-col items-center md:items-end">
              <p className="text-gray-300 font-medium">© 2023 RithFlix. All rights reserved.</p>
              <p className="text-sm text-gray-500 mt-2">
                Powered by TMDB API. Not affiliated with any streaming service.
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
