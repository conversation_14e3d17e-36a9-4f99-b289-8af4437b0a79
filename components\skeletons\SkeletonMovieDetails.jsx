import { Skeleton } from "@/components/ui/skeleton";
import SkeletonInfoPanel from "./SkeletonInfoPanel";

function SkeletonMovieDetails() {
  return (
    <main className="pt-16 pb-16">
      {/* Main Content */}
      <div className="container mx-auto px-4 mt-8">
        {/* Fixed height container for main content */}
        <div className="flex flex-col lg:flex-row gap-8 lg:h-[800px]">
          {/* Left Column - Movie and Details */}
          <div className="w-full lg:w-8/12 lg:pr-4">
            {/* Movie Player Section */}
            <div className="mb-8 rounded-xl overflow-hidden shadow-2xl shadow-black/30 border border-gray-800/50">
              <div className="aspect-video w-full">
                <Skeleton className="w-full h-full" />
              </div>
            </div>

            {/* Movie Details Section */}
            <div className="bg-zinc-900/30 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 mb-8">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-8 w-48" />
              </div>
              <Skeleton className="h-[1px] w-full mb-4" />

              {/* Overview */}
              <div className="mb-6">
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </div>

              {/* Production details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
                <div>
                  <Skeleton className="h-6 w-32 mb-2" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            </div>

            {/* Cast Section */}
            <div className="bg-zinc-900/30 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50 mb-8">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-8 w-24" />
              </div>
              <Skeleton className="h-[1px] w-full mb-6" />

              {/* Cast carousel */}
              <div className="relative">
                <div className="flex space-x-4 overflow-hidden">
                  {Array.from({ length: 5 }).map((_, index) => (
                    <div key={index} className="flex-none w-[150px]">
                      <div className="p-1">
                        <div className="rounded-lg overflow-hidden bg-zinc-800 border border-gray-700/50 h-full">
                          <div className="aspect-[2/3] relative">
                            <Skeleton className="w-full h-full" />
                          </div>
                          <div className="p-3">
                            <Skeleton className="h-4 w-full mb-1" />
                            <Skeleton className="h-3 w-2/3" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Carousel controls */}
                <div className="absolute -left-4 top-1/2 -translate-y-1/2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
                <div className="absolute -right-4 top-1/2 -translate-y-1/2">
                  <Skeleton className="h-8 w-8 rounded-full" />
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Info Panel */}
          <div className="w-full lg:w-4/12 lg:h-full">
            <SkeletonInfoPanel />
          </div>
        </div>
      </div>

      {/* Recommendations Section */}
      <div className="container mx-auto px-4 mt-12 mb-8">
        <div className="flex items-center justify-between mb-6">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32 rounded-md" />
        </div>

        {/* Recommendations carousel */}
        <div className="relative">
          <div className="flex space-x-4 overflow-hidden">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="flex-none w-[180px]">
                <div className="p-1">
                  <div className="rounded-lg overflow-hidden bg-zinc-800/50 border border-gray-700/30 h-full">
                    <div className="aspect-[2/3] relative">
                      <Skeleton className="w-full h-full" />
                    </div>
                    <div className="p-3">
                      <Skeleton className="h-4 w-full mb-1" />
                      <div className="flex items-center justify-between mt-1">
                        <Skeleton className="h-3 w-12" />
                        <Skeleton className="h-3 w-8" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Carousel controls */}
          <div className="absolute -left-4 top-1/2 -translate-y-1/2">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
          <div className="absolute -right-4 top-1/2 -translate-y-1/2">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </div>
    </main>
  );
}

export default SkeletonMovieDetails;
