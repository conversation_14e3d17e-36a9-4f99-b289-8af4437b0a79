"use client";

import { useEffect } from "react";
import { motion } from "motion/react";
import FeaturedCarousel from "@/components/FeaturedCarousel";
import MediaGrid from "@/components/MediaGrid";
import useMovieStore from "@/store/movieStore";
import useTVShowStore from "@/store/tvShowStore";
import SkeletonFeaturedCarousel from "@/components/skeletons/SkeletonFeaturedCarousel";
import SkeletonMediaGrid from "@/components/skeletons/SkeletonMediaGrid";

const HomePage = () => {
  const {
    trendingMoviesData,
    trendingMoviesLoading,
    trendingMoviesError,
    fetchTrendingMovies,
    trendingMoviesFetching,
    popularMoviesData,
    popularMoviesLoading,
    popularMoviesError,
    popularMoviesFetching,
    fetchPopularMovies,
  } = useMovieStore();

  const {
    trendingTVData,
    trendingTVLoading,
    trendingTVError,
    trendingTVFetching,
    fetchTrendingTV,
    popularTVData,
    popularTVLoading,
    popularTVError,
    popularTVFetching,
    fetchPopularTV,
  } = useTVShowStore();

  useEffect(() => {
    // Fetch initial trending movies if data is not already loaded
    if (!trendingMoviesData) {
      fetchTrendingMovies();
    }
    if (!trendingTVData) {
      fetchTrendingTV();
    }
    if (!popularMoviesData) {
      fetchPopularMovies();
    }
    if (!popularTVData) {
      fetchPopularTV();
    }
  }, [
    fetchTrendingMovies,
    trendingMoviesData,
    fetchPopularMovies,
    popularMoviesData,
    fetchTrendingTV,
    trendingTVData,
    fetchPopularTV,
    popularTVData,
  ]);

  if (trendingMoviesLoading && !trendingMoviesData?.results?.length) {
    // Show skeleton loaders for the initial fetch
    return (
      <main className="pt-20 pb-16">
        <div className="mb-16 md:px-8 lg:px-12 xl:px-16 md:block hidden">
          <SkeletonFeaturedCarousel />
        </div>
        <div className="space-y-8">
          <SkeletonMediaGrid title="Trending Movies" />
          <SkeletonMediaGrid title="Trending TV Shows" />
          <SkeletonMediaGrid title="Popular Movies" />
          <SkeletonMediaGrid title="Popular TV Shows" />
        </div>
      </main>
    );
  }

  if (trendingMoviesError) {
    return (
      <main className="pt-20 pb-16 flex justify-center items-center min-h-screen">
        <p>Error loading movies: {trendingMoviesError.message}</p>
      </main>
    );
  }

  return (
    <main className="pt-20 pb-16">

      {/* Hero Section with Featured Carousel */}
      <motion.div
        className="mb-16 md:px-8 lg:px-12 xl:px-16 md:block hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <FeaturedCarousel
          items={trendingMoviesData?.results || []}
          type="movie"
          loading={trendingMoviesLoading && !trendingMoviesData?.results?.length}
        />
      </motion.div>

      <div className="space-y-8">
        {trendingMoviesData?.results &&
          trendingMoviesData.results.length > 0 && (
            <MediaGrid
              title="Trending Movies"
              data={trendingMoviesData.results}
              type="movie"
              loading={trendingMoviesLoading || trendingMoviesFetching}
              error={trendingMoviesError}
            />
          )}

        {/* Trending TV Shows Section */}
        {trendingTVData?.results && trendingTVData.results.length > 0 && (
          <MediaGrid
            title="Trending TV Shows"
            data={trendingTVData?.results || []}
            type="tv"
            loading={trendingTVLoading || trendingTVFetching}
            error={trendingTVError}
          />
        )}

        {/* Popular Movies Section */}
        {popularMoviesData?.results && popularMoviesData.results.length > 0 && (
          <MediaGrid
            title="Popular Movies"
            data={popularMoviesData?.results || []}
            type="movie"
            loading={popularMoviesLoading || popularMoviesFetching}
            error={popularMoviesError}
          />
        )}

        {/* Popular TV Shows Section */}
        {popularTVData?.results && popularTVData.results.length > 0 && (
          <MediaGrid
            title="Popular TV Shows"
            data={popularTVData?.results || []}
            type="tv"
            loading={popularTVLoading || popularTVFetching}
            error={popularTVError}
          />
        )}
      </div>
    </main>
  );
};

export default HomePage;
