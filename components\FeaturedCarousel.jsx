import React from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "./ui/carousel";
import { Star } from "lucide-react";
import Link from "next/link";
import { Button } from "./ui/button";
import { Play } from "lucide-react";
import { getImageUrl } from "@/lib/image";
import SkeletonFeaturedCarousel from "./skeletons/SkeletonFeaturedCarousel";

function FeaturedCarousel({ items, type, loading = false }) {
  if (loading) {
    return <SkeletonFeaturedCarousel />;
  }

  if (!items || items.length === 0) {
    return null;
  }

  const isMovie = (item) => {
    return "title" in item;
  };

  const getTitle = (item) => {
    return isMovie(item) ? item.title : item.name;
  };

  const getDate = (item) => {
    return isMovie(item) ? item.release_date : item.first_air_date;
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.getFullYear().toString();
  };

  return (
    <Carousel className="w-full relative">
      <CarouselContent>
        {items.slice(0, 10).map((item) => (
          <CarouselItem key={item.id}>
            <div className="relative w-full h-[70vh] overflow-hidden rounded-lg">
              {/* Backdrop image */}
              <div
                className="absolute inset-0 bg-cover bg-center bg-no-repeat"
                style={{
                  backgroundImage: `url(${getImageUrl(
                    item.backdrop_path,
                    "w780"
                  )})`,
                }}
              >
                {/* Gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-background-dark via-background-dark/80 to-transparent" />
              </div>

              {/* Content */}
              <div className="absolute inset-0 flex flex-col justify-end p-8 md:p-12">
                <div className="max-w-3xl animate-fade-in">
                  <h2 className="text-3xl md:text-5xl font-bold mb-3">
                    {getTitle(item)}
                  </h2>

                  <div className="flex items-center gap-4 mb-4">
                    {item.vote_average ? (
                      <div className="flex items-center">
                        <Star className="h-5 w-5 text-yellow-400 fill-yellow-400 mr-1" />
                        <span>{item.vote_average.toFixed(1)}/10</span>
                      </div>
                    ) : null}

                    {getDate(item) ? (
                      <div className="text-gray-300">
                        {formatDate(getDate(item))}
                      </div>
                    ) : null}
                  </div>

                  <p className="text-gray-200 mb-6 line-clamp-2 md:line-clamp-3">
                    {item.overview}
                  </p>

                  <Link href={`/${type}/${item.id}`}>
                    <Button className="bg-primary-red hover:bg-primary-red/90 text-white font-medium px-6 py-2 flex items-center gap-2">
                      <Play className="h-4 w-4" />
                      <span className="text-white">Watch Now</span>
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>

      <CarouselPrevious className="left-4 bg-black/50 text-white hover:bg-black/70" />
      <CarouselNext className="right-4 bg-black/50 text-white hover:bg-black/70" />
    </Carousel>
  );
}

export default FeaturedCarousel;
