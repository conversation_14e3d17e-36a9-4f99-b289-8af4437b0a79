import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { VIDEO_SOURCES } from "@/lib/api";
import { AlertCircle, RefreshCw } from "lucide-react";

interface VideoPlayerProps {
  tmdbId: number;
  type: "movie" | "tv";
  season?: number;
  episode?: number;
}

type SourceKey = keyof typeof VIDEO_SOURCES.movie;

const VideoPlayer = ({
  tmdbId,
  type,
  season = 1,
  episode = 1,
}: VideoPlayerProps) => {
  const [currentSource, setCurrentSource] = useState<SourceKey>("server 1");
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Get the appropriate URL based on the source and media type
  const getSourceUrl = () => {
    if (type === "movie") {
      return VIDEO_SOURCES.movie[currentSource](tmdbId);
    } else {
      return VIDEO_SOURCES.tv[currentSource](tmdbId, season, episode);
    }
  };

  // Reset loading/error states when source, season, or episode changes
  useEffect(() => {
    setIsLoading(true);
    setHasError(false);
  }, [currentSource, season, episode, tmdbId]);

  // Handle iframe load events
  const handleIframeLoad = () => {
    setIsLoading(false);
  };

  // Handle iframe error events
  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div className='w-full bg-black rounded-lg overflow-hidden'>
      <div className='relative aspect-video h-[450px] w-full md:w-full md:h-full '>
        {/* Loading indicator */}
        {isLoading && (
          <div className='absolute inset-0 flex items-center justify-center bg-black'>
            <RefreshCw className='h-10 w-10 text-primary-red animate-spin' />
          </div>
        )}

        {/* Error message */}
        {hasError && (
          <div className='absolute inset-0 flex flex-col items-center justify-center bg-black p-4'>
            <AlertCircle className='h-10 w-10 text-primary-red mb-2' />
            <h3 className='text-lg font-semibold mb-2'>
              Video source failed to load
            </h3>
            <p className='text-sm text-gray-400 mb-4 text-center'>
              Please try another source or check back later
            </p>
            <Button
              onClick={() => {
                setIsLoading(true);
                setHasError(false);
              }}
              className='bg-primary-red hover:bg-primary-red/80 text-white font-medium px-4'
            >
              <span className='text-white'>Retry</span>
            </Button>
          </div>
        )}

        {/* Video iframe */}
        <iframe
          src={getSourceUrl()}
          className={`absolute inset-0 w-full h-full ${
            hasError ? "hidden" : ""
          }`}
          allowFullScreen
          onLoad={handleIframeLoad}
          onError={handleIframeError}
        ></iframe>
      </div>

      {/* Source selector */}
      <div className='p-4 bg-card-dark'>
        <div className='flex flex-wrap items-center gap-2 text-black'>
          <span className='text-sm text-gray-400'>Source:</span>
          <Select
            value={currentSource}
            onValueChange={(value) => setCurrentSource(value as SourceKey)}
          >
            <SelectTrigger className='w-[140px]'>
              <SelectValue placeholder='Select source' />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(VIDEO_SOURCES[type]).map((sourceKey) => (
                <SelectItem key={sourceKey} value={sourceKey}>
                  {sourceKey.toUpperCase()}
                </SelectItem>
              ))}
              {/* <SelectItem value="vidEasy">VidEasy</SelectItem>
              <SelectItem value="vidSrc">VidSrc</SelectItem>
              <SelectItem value="riveStream">RiveStream</SelectItem>
              <SelectItem value="vidZee">VidZee</SelectItem>
              <SelectItem value="autoEmbed">AutoEmbed</SelectItem> */}
            </SelectContent>
          </Select>

          <div className='flex-grow'></div>

          <Button
            variant='outline'
            size='sm'
            onClick={() => {
              setIsLoading(true);
              setHasError(false);
            }}
            className='text-xs'
          >
            <RefreshCw className='h-3 w-3 mr-1' />
            <span className='text-black'>Refresh</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoPlayer;
